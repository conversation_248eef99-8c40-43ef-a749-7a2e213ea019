import { Head, Link, useForm } from '@inertiajs/react';
import React from 'react';
import Layout from '../Layout';

interface Domain {
    id: string;
    slug: string;
    verified: boolean;
    primary: boolean;
}

interface Tag {
    id: string;
    name: string;
    color: string;
}

interface LinkType {
    id: string;
    key: string;
    url: string;
    domain: string;
    title?: string;
    description?: string;
    clicks: number;
    created_at: string;
    tags: Array<{
        id: string;
        name: string;
        color: string;
    }>;
}

interface Props {
    project: {
        id: string;
        name: string;
        slug: string;
    };
    link: LinkType;
    domains: Domain[];
    tags: Tag[];
}

export default function EditLink({ project, link, domains, tags }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        url: link.url,
        key: link.key,
        domain: link.domain,
        title: link.title || '',
        description: link.description || '',
        tags: link.tags.map(tag => tag.id),
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/projects/${project.slug}/links/${link.id}`, {
            onSuccess: () => {
                // Redirect will be handled by Inertia
            }
        });
    };

    return (
        <Layout>
            <Head title="Edit Link" />

            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8">
                    <div className="flex items-center space-x-4">
                        <Link
                            href={`/projects/${project.slug}/links`}
                            className="text-gray-400 hover:text-gray-500"
                        >
                            <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M7.707 14.707a1 1 0 01-1.414 0L3.586 12l2.707-2.707a1 1 0 011.414 1.414L6.414 12l1.293 1.293a1 1 0 010 1.414z" clipRule="evenodd" />
                                <path fillRule="evenodd" d="M4.293 12.293a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L7.414 14H17a1 1 0 100-2H7.414l1.293-1.293a1 1 0 00-1.414-1.414l-3 3z" clipRule="evenodd" />
                            </svg>
                        </Link>
                        <div>
                            <h1 className="text-2xl font-bold text-gray-900">Edit Link</h1>
                            <p className="text-gray-600 mt-1">Update link for {project.name}</p>
                        </div>
                    </div>
                </div>

                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <div className="flex items-center justify-between">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Link Details</h3>
                            <div className="text-sm text-gray-500">
                                {link.clicks} clicks
                            </div>
                        </div>
                    </div>

                    <form onSubmit={handleSubmit} className="p-6 space-y-6">
                        {/* Destination URL */}
                        <div>
                            <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                                Destination URL *
                            </label>
                            <div className="mt-1">
                                <input
                                    type="url"
                                    id="url"
                                    required
                                    value={data.url}
                                    onChange={(e) => setData('url', e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="https://example.com"
                                />
                                {errors.url && (
                                    <p className="mt-1 text-sm text-red-600">{errors.url}</p>
                                )}
                            </div>
                        </div>

                        {/* Short Link */}
                        <div>
                            <label htmlFor="short-link" className="block text-sm font-medium text-gray-700">
                                Short Link
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                                <select
                                    value={data.domain}
                                    onChange={(e) => setData('domain', e.target.value)}
                                    className="border-gray-300 rounded-l-md focus:ring-black focus:border-black"
                                >
                                    {domains.map((domain) => (
                                        <option key={domain.id} value={domain.slug}>
                                            {domain.slug}
                                        </option>
                                    ))}
                                </select>
                                <span className="inline-flex items-center px-3 border-t border-b border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                    /
                                </span>
                                <input
                                    type="text"
                                    id="short-link"
                                    value={data.key}
                                    onChange={(e) => setData('key', e.target.value)}
                                    className="flex-1 border-gray-300 rounded-r-md focus:ring-black focus:border-black"
                                    placeholder="custom-key"
                                />
                            </div>
                            {errors.key && (
                                <p className="mt-1 text-sm text-red-600">{errors.key}</p>
                            )}
                        </div>

                        {/* Title */}
                        <div>
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                                Title
                            </label>
                            <div className="mt-1">
                                <input
                                    type="text"
                                    id="title"
                                    value={data.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="Optional title for this link"
                                />
                            </div>
                        </div>

                        {/* Description */}
                        <div>
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                                Description
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="description"
                                    rows={3}
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="Optional description"
                                />
                            </div>
                        </div>

                        {/* Tags */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Tags</label>
                            <div className="mt-2 space-y-2">
                                {tags.map((tag) => (
                                    <label key={tag.id} className="inline-flex items-center mr-4">
                                        <input
                                            type="checkbox"
                                            checked={data.tags.includes(tag.id)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setData('tags', [...data.tags, tag.id]);
                                                } else {
                                                    setData('tags', data.tags.filter(id => id !== tag.id));
                                                }
                                            }}
                                            className="rounded border-gray-300 text-black focus:ring-black"
                                        />
                                        <span
                                            className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                            style={{ backgroundColor: tag.color + '20', color: tag.color }}
                                        >
                                            {tag.name}
                                        </span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Submit */}
                        <div className="flex justify-end space-x-3">
                            <Link
                                href={`/projects/${project.slug}/links`}
                                className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                            >
                                Cancel
                            </Link>
                            <button
                                type="submit"
                                disabled={processing}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50"
                            >
                                {processing ? 'Updating...' : 'Update Link'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </Layout>
    );
}
