import { Head, useForm } from '@inertiajs/react';
import React, { useState } from 'react';
import Layout from '../Layout';

interface Domain {
    id: string;
    slug: string;
    verified: boolean;
    primary: boolean;
}

interface Tag {
    id: string;
    name: string;
    color: string;
}

interface Props {
    project: {
        id: string;
        name: string;
        slug: string;
    };
    domains: Domain[];
    tags: Tag[];
}

export default function CreateLink({ project, domains, tags }: Props) {
    const { data, setData, post, processing, errors } = useForm({
        url: '',
        key: '',
        domain: domains.find(d => d.primary)?.slug || domains[0]?.slug || '',
        title: '',
        description: '',
        tags: [] as string[],
        expires_at: '',
        password: '',
        track_conversion: true,
        public_stats: false,
    });

    const [generateKey, setGenerateKey] = useState(true);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(`/projects/${project.slug}/links`, {
            onSuccess: () => {
                // Redirect will be handled by Inertia
            }
        });
    };

    const generateRandomKey = () => {
        const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 6; i++) {
            result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
        setData('key', result);
    };

    const handleUrlChange = (url: string) => {
        setData('url', url);

        // Auto-generate key if enabled and key is empty
        if (generateKey && !data.key) {
            generateRandomKey();
        }
    };

    return (
        <Layout>
            <Head title="Create Link" />

            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">Create Short Link</h1>
                    <p className="text-gray-600 mt-1">Create a new short link for {project.name}</p>
                </div>

                <div className="bg-white shadow rounded-lg">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">Link Details</h3>
                    </div>

                    <form onSubmit={handleSubmit} className="p-6 space-y-6">
                        {/* Destination URL */}
                        <div>
                            <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                                Destination URL *
                            </label>
                            <div className="mt-1">
                                <input
                                    type="url"
                                    id="url"
                                    required
                                    value={data.url}
                                    onChange={(e) => handleUrlChange(e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="https://example.com"
                                />
                                {errors.url && (
                                    <p className="mt-1 text-sm text-red-600">{errors.url}</p>
                                )}
                            </div>
                        </div>

                        {/* Short Link */}
                        <div>
                            <label htmlFor="short-link" className="block text-sm font-medium text-gray-700">
                                Short Link
                            </label>
                            <div className="mt-1 flex rounded-md shadow-sm">
                                <select
                                    value={data.domain}
                                    onChange={(e) => setData('domain', e.target.value)}
                                    className="border-gray-300 rounded-l-md focus:ring-black focus:border-black"
                                >
                                    {domains.map((domain) => (
                                        <option key={domain.id} value={domain.slug}>
                                            {domain.slug}
                                        </option>
                                    ))}
                                </select>
                                <span className="inline-flex items-center px-3 border-t border-b border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                    /
                                </span>
                                <input
                                    type="text"
                                    id="short-link"
                                    value={data.key}
                                    onChange={(e) => setData('key', e.target.value)}
                                    className="flex-1 border-gray-300 rounded-r-md focus:ring-black focus:border-black"
                                    placeholder="custom-key"
                                />
                                <button
                                    type="button"
                                    onClick={generateRandomKey}
                                    className="ml-2 inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                                >
                                    Random
                                </button>
                            </div>
                            {errors.key && (
                                <p className="mt-1 text-sm text-red-600">{errors.key}</p>
                            )}
                        </div>

                        {/* Title */}
                        <div>
                            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                                Title
                            </label>
                            <div className="mt-1">
                                <input
                                    type="text"
                                    id="title"
                                    value={data.title}
                                    onChange={(e) => setData('title', e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="Optional title for this link"
                                />
                            </div>
                        </div>

                        {/* Description */}
                        <div>
                            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                                Description
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="description"
                                    rows={3}
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    placeholder="Optional description"
                                />
                            </div>
                        </div>

                        {/* Tags */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700">Tags</label>
                            <div className="mt-2 space-y-2">
                                {tags.map((tag) => (
                                    <label key={tag.id} className="inline-flex items-center mr-4">
                                        <input
                                            type="checkbox"
                                            checked={data.tags.includes(tag.id)}
                                            onChange={(e) => {
                                                if (e.target.checked) {
                                                    setData('tags', [...data.tags, tag.id]);
                                                } else {
                                                    setData('tags', data.tags.filter(id => id !== tag.id));
                                                }
                                            }}
                                            className="rounded border-gray-300 text-black focus:ring-black"
                                        />
                                        <span
                                            className="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                                            style={{ backgroundColor: tag.color + '20', color: tag.color }}
                                        >
                                            {tag.name}
                                        </span>
                                    </label>
                                ))}
                            </div>
                        </div>

                        {/* Advanced Options */}
                        <div className="border-t border-gray-200 pt-6">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Advanced Options</h4>

                            <div className="space-y-4">
                                {/* Track Conversion */}
                                <div className="flex items-center">
                                    <input
                                        id="track-conversion"
                                        type="checkbox"
                                        checked={data.track_conversion}
                                        onChange={(e) => setData('track_conversion', e.target.checked)}
                                        className="rounded border-gray-300 text-black focus:ring-black"
                                    />
                                    <label htmlFor="track-conversion" className="ml-2 text-sm text-gray-700">
                                        Track conversions
                                    </label>
                                </div>

                                {/* Public Stats */}
                                <div className="flex items-center">
                                    <input
                                        id="public-stats"
                                        type="checkbox"
                                        checked={data.public_stats}
                                        onChange={(e) => setData('public_stats', e.target.checked)}
                                        className="rounded border-gray-300 text-black focus:ring-black"
                                    />
                                    <label htmlFor="public-stats" className="ml-2 text-sm text-gray-700">
                                        Make stats publicly accessible
                                    </label>
                                </div>
                            </div>
                        </div>

                        {/* Submit */}
                        <div className="flex justify-end space-x-3">
                            <button
                                type="button"
                                onClick={() => history.back()}
                                className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                disabled={processing}
                                className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50"
                            >
                                {processing ? 'Creating...' : 'Create Link'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </Layout>
    );
}
