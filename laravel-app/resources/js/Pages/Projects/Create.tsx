import { Head, useForm } from '@inertiajs/react';
import React from 'react';
import Layout from '../Layout';

export default function CreateProject() {
    const { data, setData, post, processing, errors } = useForm({
        name: '',
        description: '',
        plan: 'free',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post('/projects');
    };

    const plans = [
        {
            value: 'free',
            name: 'Free',
            description: 'Perfect for getting started',
            features: ['25 links', '1,000 clicks/month', '3 custom domains'],
            price: '$0',
        },
        {
            value: 'pro',
            name: 'Pro',
            description: 'Best for growing teams',
            features: ['1,000 links', '25,000 clicks/month', '10 custom domains'],
            price: '$24',
        },
        {
            value: 'business',
            name: 'Business',
            description: 'For larger organizations',
            features: ['5,000 links', '150,000 clicks/month', '40 custom domains'],
            price: '$99',
        },
    ];

    return (
        <Layout>
            <Head title="Create Project" />

            <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold text-gray-900">Create a new project</h1>
                    <p className="text-gray-600 mt-2">
                        Get started by creating a project to organize your links and track analytics.
                    </p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-8">
                    {/* Project Details */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">Project Details</h3>

                        <div className="space-y-6">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                                    Project Name
                                </label>
                                <input
                                    type="text"
                                    id="name"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="My Awesome Project"
                                    required
                                />
                                {errors.name && (
                                    <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                                )}
                            </div>

                            <div>
                                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                                    Description (Optional)
                                </label>
                                <textarea
                                    id="description"
                                    rows={3}
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                    placeholder="Brief description of your project..."
                                />
                                {errors.description && (
                                    <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Plan Selection */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-6">Choose a Plan</h3>

                        <div className="grid gap-6 md:grid-cols-3">
                            {plans.map((plan) => (
                                <div
                                    key={plan.value}
                                    className={`relative rounded-lg border p-6 cursor-pointer transition-all ${
                                        data.plan === plan.value
                                            ? 'border-blue-500 ring-2 ring-blue-500 ring-opacity-50'
                                            : 'border-gray-300 hover:border-gray-400'
                                    }`}
                                    onClick={() => setData('plan', plan.value)}
                                >
                                    <input
                                        type="radio"
                                        name="plan"
                                        value={plan.value}
                                        checked={data.plan === plan.value}
                                        onChange={() => setData('plan', plan.value)}
                                        className="sr-only"
                                    />

                                    <div className="flex items-center justify-between mb-3">
                                        <h4 className="text-lg font-semibold text-gray-900">{plan.name}</h4>
                                        <span className="text-2xl font-bold text-gray-900">{plan.price}</span>
                                    </div>

                                    <p className="text-gray-600 text-sm mb-4">{plan.description}</p>

                                    <ul className="space-y-2">
                                        {plan.features.map((feature, index) => (
                                            <li key={index} className="flex items-center text-sm text-gray-600">
                                                <svg className="h-4 w-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                                </svg>
                                                {feature}
                                            </li>
                                        ))}
                                    </ul>

                                    {data.plan === plan.value && (
                                        <div className="absolute top-0 right-0 transform translate-x-2 -translate-y-2">
                                            <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                Selected
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>

                        {errors.plan && (
                            <p className="mt-3 text-sm text-red-600">{errors.plan}</p>
                        )}
                    </div>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-4">
                        <button
                            type="button"
                            onClick={() => window.history.back()}
                            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={processing}
                            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {processing ? 'Creating...' : 'Create Project'}
                        </button>
                    </div>
                </form>
            </div>
        </Layout>
    );
}
