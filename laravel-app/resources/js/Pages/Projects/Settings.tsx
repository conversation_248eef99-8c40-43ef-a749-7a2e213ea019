import { Head, useForm } from '@inertiajs/react';
import React from 'react';
import Layout from '../Layout';

interface Props {
    project: {
        id: string;
        name: string;
        slug: string;
        description?: string;
        plan: string;
        usage_limit: number;
        usage_count: number;
        links_usage: number;
        links_limit: number;
        domains_limit: number;
        tags_limit: number;
        users_limit: number;
    };
}

export default function ProjectSettings({ project }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        name: project.name,
        description: project.description || '',
        slug: project.slug,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(`/api/projects/${project.id}`);
    };

    const usagePercentage = (project.usage_count / project.usage_limit) * 100;
    const linksPercentage = (project.links_usage / project.links_limit) * 100;

    return (
        <Layout>
            <Head title="Project Settings" />

            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="mb-8">
                    <h1 className="text-2xl font-bold text-gray-900">Project Settings</h1>
                    <p className="text-gray-600 mt-1">Manage settings for {project.name}</p>
                </div>

                <div className="space-y-6">
                    {/* Project Details */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Project Details</h3>
                        </div>

                        <form onSubmit={handleSubmit} className="p-6 space-y-6">
                            <div>
                                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                                    Project Name
                                </label>
                                <div className="mt-1">
                                    <input
                                        type="text"
                                        id="name"
                                        required
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    />
                                    {errors.name && (
                                        <p className="mt-1 text-sm text-red-600">{errors.name}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <label htmlFor="slug" className="block text-sm font-medium text-gray-700">
                                    Project Slug
                                </label>
                                <div className="mt-1">
                                    <input
                                        type="text"
                                        id="slug"
                                        required
                                        value={data.slug}
                                        onChange={(e) => setData('slug', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                    />
                                    {errors.slug && (
                                        <p className="mt-1 text-sm text-red-600">{errors.slug}</p>
                                    )}
                                </div>
                            </div>

                            <div>
                                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                                    Description
                                </label>
                                <div className="mt-1">
                                    <textarea
                                        id="description"
                                        rows={3}
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-black focus:border-black"
                                        placeholder="Optional project description"
                                    />
                                </div>
                            </div>

                            <div className="flex justify-end">
                                <button
                                    type="submit"
                                    disabled={processing}
                                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black disabled:opacity-50"
                                >
                                    {processing ? 'Saving...' : 'Save Changes'}
                                </button>
                            </div>
                        </form>
                    </div>

                    {/* Usage & Limits */}
                    <div className="bg-white shadow rounded-lg">
                        <div className="px-6 py-4 border-b border-gray-200">
                            <h3 className="text-lg leading-6 font-medium text-gray-900">Usage & Limits</h3>
                            <p className="mt-1 text-sm text-gray-500">Current plan: <span className="font-medium capitalize">{project.plan}</span></p>
                        </div>

                        <div className="p-6 space-y-6">
                            {/* Overall Usage */}
                            <div>
                                <div className="flex justify-between text-sm">
                                    <span className="font-medium text-gray-700">Overall Usage</span>
                                    <span className="text-gray-500">
                                        {project.usage_count.toLocaleString()} / {project.usage_limit.toLocaleString()}
                                    </span>
                                </div>
                                <div className="mt-2 bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                                    />
                                </div>
                            </div>

                            {/* Links Usage */}
                            <div>
                                <div className="flex justify-between text-sm">
                                    <span className="font-medium text-gray-700">Links</span>
                                    <span className="text-gray-500">
                                        {project.links_usage} / {project.links_limit}
                                    </span>
                                </div>
                                <div className="mt-2 bg-gray-200 rounded-full h-2">
                                    <div
                                        className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${Math.min(linksPercentage, 100)}%` }}
                                    />
                                </div>
                            </div>

                            {/* Other Limits */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="text-center p-4 border border-gray-200 rounded-lg">
                                    <div className="text-2xl font-bold text-gray-900">{project.domains_limit}</div>
                                    <div className="text-sm text-gray-500">Domains</div>
                                </div>
                                <div className="text-center p-4 border border-gray-200 rounded-lg">
                                    <div className="text-2xl font-bold text-gray-900">{project.tags_limit}</div>
                                    <div className="text-sm text-gray-500">Tags</div>
                                </div>
                                <div className="text-center p-4 border border-gray-200 rounded-lg">
                                    <div className="text-2xl font-bold text-gray-900">{project.users_limit}</div>
                                    <div className="text-sm text-gray-500">Team Members</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Danger Zone */}
                    <div className="bg-white shadow rounded-lg border-red-200 border">
                        <div className="px-6 py-4 border-b border-red-200 bg-red-50">
                            <h3 className="text-lg leading-6 font-medium text-red-900">Danger Zone</h3>
                        </div>

                        <div className="p-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="text-sm font-medium text-gray-900">Delete Project</h4>
                                    <p className="text-sm text-gray-500">
                                        Permanently delete this project and all associated data. This action cannot be undone.
                                    </p>
                                </div>
                                <button
                                    type="button"
                                    className="inline-flex justify-center py-2 px-4 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                >
                                    Delete Project
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </Layout>
    );
}
