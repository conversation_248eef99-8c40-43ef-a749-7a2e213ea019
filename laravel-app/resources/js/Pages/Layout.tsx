import { Link, router, usePage } from '@inertiajs/react';
import React, { useEffect, useState } from 'react';

interface LayoutProps {
    children: React.ReactNode;
}

interface User {
    id: string;
    name: string;
    email: string;
    image?: string;
}

interface PageProps {
    auth?: {
        user: User;
    };
    project?: {
        id: string;
        name: string;
        slug: string;
    };
    flash?: {
        success?: string;
        error?: string;
    };
    [key: string]: any;
}

export default function Layout({ children }: LayoutProps) {
    const { props } = usePage<PageProps>();
    const { auth, project, flash } = props;
    const [showFlash, setShowFlash] = useState(false);
    const [showUserMenu, setShowUserMenu] = useState(false);

    useEffect(() => {
        if (flash?.success || flash?.error) {
            setShowFlash(true);
            const timer = setTimeout(() => setShowFlash(false), 5000);
            return () => clearTimeout(timer);
        }
    }, [flash]);

    const handleLogout = () => {
        router.post('/logout');
    };

    const getUserInitials = (name: string) => {
        return name
            .split(' ')
            .map(n => n[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Navigation */}
            <nav className="bg-white shadow-sm border-b border-gray-200">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between h-16">
                        <div className="flex items-center">
                            {/* Logo */}
                            <Link href="/" className="flex-shrink-0 flex items-center">
                                <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                                    <span className="text-white font-bold text-sm">D</span>
                                </div>
                                <span className="ml-2 text-xl font-bold text-gray-900">Dub</span>
                            </Link>

                            {/* Project Navigation */}
                            {project && (
                                <div className="hidden md:ml-8 md:flex md:items-center md:space-x-1">
                                    <span className="text-gray-400">/</span>
                                    <span className="ml-2 text-lg font-medium text-gray-900">{project.name}</span>
                                </div>
                            )}
                        </div>

                        {/* Center Navigation */}
                        {project && (
                            <div className="hidden md:flex md:items-center md:space-x-8">
                                <Link
                                    href={`/projects/${project.slug}/links`}
                                    className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-900 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M12.586 4.586a2 2 0 112.828 2.828l-3 3a2 2 0 01-2.828 0 1 1 0 00-1.414 1.414 4 4 0 005.656 0l3-3a4 4 0 00-5.656-5.656l-1.5 1.5a1 1 0 101.414 1.414l1.5-1.5zm-5 5a2 2 0 012.828 0 1 1 0 101.414-1.414 4 4 0 00-5.656 0l-3 3a4 4 0 105.656 5.656l1.5-1.5a1 1 0 10-1.414-1.414l-1.5 1.5a2 2 0 11-2.828-2.828l3-3z" clipRule="evenodd" />
                                    </svg>
                                    Links
                                </Link>
                                <Link
                                    href={`/projects/${project.slug}/analytics`}
                                    className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-900 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                                    </svg>
                                    Analytics
                                </Link>
                                <Link
                                    href={`/projects/${project.slug}/settings`}
                                    className="inline-flex items-center px-1 pt-1 text-sm font-medium text-gray-900 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300"
                                >
                                    <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                                    </svg>
                                    Settings
                                </Link>
                            </div>
                        )}

                        {/* Right side */}
                        <div className="flex items-center space-x-4">
                            {/* User menu */}
                            {auth?.user ? (
                                <div className="relative">
                                    <button
                                        onClick={() => setShowUserMenu(!showUserMenu)}
                                        className="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        {auth.user.image ? (
                                            <img
                                                className="w-8 h-8 rounded-full"
                                                src={auth.user.image}
                                                alt={auth.user.name}
                                            />
                                        ) : (
                                            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                                <span className="text-xs font-medium text-gray-700">
                                                    {getUserInitials(auth.user.name)}
                                                </span>
                                            </div>
                                        )}
                                        <span className="text-sm text-gray-700">{auth.user.name}</span>
                                        <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                                        </svg>
                                    </button>

                                    {/* Dropdown menu */}
                                    {showUserMenu && (
                                        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
                                            <div className="py-1">
                                                <a
                                                    href="#"
                                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                >
                                                    Your Profile
                                                </a>
                                                <a
                                                    href="#"
                                                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                >
                                                    Settings
                                                </a>
                                                <button
                                                    onClick={handleLogout}
                                                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                                                >
                                                    Sign out
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="flex items-center space-x-4">
                                    <Link
                                        href="/login"
                                        className="text-gray-500 hover:text-gray-700"
                                    >
                                        Sign in
                                    </Link>
                                    <Link
                                        href="/register"
                                        className="bg-black text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-800"
                                    >
                                        Sign up
                                    </Link>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </nav>

            {/* Flash Messages */}
            {showFlash && flash?.success && (
                <div className="fixed top-16 inset-x-0 z-50 px-4">
                    <div className="p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg shadow-md">
                        {flash.success}
                    </div>
                </div>
            )}
            {showFlash && flash?.error && (
                <div className="fixed top-16 inset-x-0 z-50 px-4">
                    <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg shadow-md">
                        {flash.error}
                    </div>
                </div>
            )}

            {/* Page content */}
            <main>{children}</main>
        </div>
    );
}
