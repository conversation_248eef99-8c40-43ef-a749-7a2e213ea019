import { Button, Input } from '@dub/ui';
import { Head, Link, useForm } from '@inertiajs/react';
import { Mail } from 'lucide-react';
import { FormEvent } from 'react';

interface ForgotPasswordData {
    email: string;
}

interface ForgotPasswordProps {
    status?: string;
}

export default function ForgotPassword({ status }: ForgotPasswordProps) {
    const { data, setData, post, processing, errors } = useForm<ForgotPasswordData>({
        email: '',
    });

    const submit = (e: FormEvent) => {
        e.preventDefault();
        post(route('password.email'));
    };

    return (
        <>
            <Head title="Forgot Password" />

            <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <div>
                        <h1 className="text-center text-3xl font-bold text-gray-900">
                            Forgot your password?
                        </h1>
                        <p className="mt-2 text-center text-sm text-gray-600">
                            No problem. Just let us know your email address and we will email you a password reset link.
                        </p>
                    </div>

                    {status && (
                        <div className="bg-green-50 border border-green-200 rounded-md p-4">
                            <div className="text-sm text-green-800">
                                {status}
                            </div>
                        </div>
                    )}

                    <form className="mt-8 space-y-6" onSubmit={submit}>
                        <div>
                            <label htmlFor="email" className="sr-only">
                                Email address
                            </label>
                            <div className="relative">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <Mail className="h-5 w-5 text-gray-400" />
                                </div>
                                <Input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    required
                                    autoFocus
                                    className="pl-10"
                                    placeholder="Email address"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    error={errors.email}
                                />
                            </div>
                        </div>

                        <div>
                            <Button
                                type="submit"
                                disabled={processing}
                                loading={processing}
                                className="w-full"
                                text="Email Password Reset Link"
                            />
                        </div>
                    </form>

                    <div className="text-center">
                        <Link
                            href={route('login')}
                            className="text-sm text-gray-600 hover:text-gray-900"
                        >
                            Back to login
                        </Link>
                    </div>
                </div>
            </div>
        </>
    );
}
