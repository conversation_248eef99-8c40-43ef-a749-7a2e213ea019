<?php

use App\Http\Controllers\Api\AnalyticsController;
use App\Http\Controllers\Api\AuthController;
use App\Http\Controllers\Api\DomainController;
use App\Http\Controllers\Api\LinkController;
use App\Http\Controllers\Api\ProjectController;
use App\Http\Controllers\Api\TagController;
use App\Http\Controllers\RedirectController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// Authentication routes (no auth required)
Route::post('/auth/login', [AuthController::class, 'login']);

// Protected authentication routes
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/auth/logout', [AuthController::class, 'logout']);
    Route::get('/auth/user', [AuthController::class, 'user']);
});

// Public analytics tracking (no auth required)
Route::post('/track', [AnalyticsController::class, 'track']);

// Protected API routes
Route::middleware('auth:sanctum')->group(function () {
    // Project routes
    Route::prefix('projects')->group(function () {
        Route::get('/', [ProjectController::class, 'index']);
        Route::post('/', [ProjectController::class, 'store']);
        Route::get('/{project}', [ProjectController::class, 'show']);
        Route::put('/{project}', [ProjectController::class, 'update']);
        Route::delete('/{project}', [ProjectController::class, 'destroy']);

        // Nested routes for project resources
        Route::prefix('{project}')->group(function () {
            // Links
            Route::apiResource('links', LinkController::class);
            Route::post('links/{link}/archive', [LinkController::class, 'archive']);
            Route::post('links/{link}/unarchive', [LinkController::class, 'unarchive']);
            Route::get('stats/links', [LinkController::class, 'stats']);

            // Domains
            Route::apiResource('domains', DomainController::class);
            Route::post('domains/{domain}/set-primary', [DomainController::class, 'setPrimary']);
            Route::post('domains/{domain}/verify', [DomainController::class, 'verify']);

            // Tags
            Route::apiResource('tags', TagController::class);
            Route::post('tags/{tag}/attach-links', [TagController::class, 'attachToLinks']);
            Route::post('tags/{tag}/detach-links', [TagController::class, 'detachFromLinks']);

            // Analytics
            Route::get('analytics', [AnalyticsController::class, 'projectOverview']);
            Route::get('links/{link}/analytics', [AnalyticsController::class, 'linkAnalytics']);
        });
    });
});

// Public redirect routes (no auth required)
Route::get('/{domain}/{key}', [RedirectController::class, 'redirect'])
    ->where('domain', '[a-zA-Z0-9\-\.]+')
    ->where('key', '.*');