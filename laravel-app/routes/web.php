<?php

use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ProjectsController;
use App\Http\Controllers\LinksController;
use App\Http\Controllers\AnalyticsController;
use App\Http\Controllers\WelcomeController;
use App\Models\Project;
use Illuminate\Support\Facades\Route;

// Route model binding for projects by slug
Route::bind('project', function ($value) {
    return Project::where('slug', $value)->firstOrFail();
});

// Root route - redirect based on authentication status
Route::get('/', function () {
    return auth()->check()
        ? redirect()->route('dashboard.index')
        : app(WelcomeController::class)->index();
})->name('home');

// Welcome page for unauthenticated users
Route::middleware('guest')->group(function () {
    Route::get('/welcome', [WelcomeController::class, 'index'])->name('welcome');

    Route::get('login', [AuthenticatedSessionController::class, 'create'])->name('login');
    Route::post('login', [AuthenticatedSessionController::class, 'store']);

    Route::get('register', [RegisteredUserController::class, 'create'])->name('register');
    Route::post('register', [RegisteredUserController::class, 'store']);

    Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])->name('password.request');
    Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])->name('password.email');

    Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])->name('password.reset');
    Route::post('reset-password', [NewPasswordController::class, 'store'])->name('password.store');
});

Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])
    ->middleware('auth')
    ->name('logout');

// Protected Routes (require authentication)
Route::middleware('auth')->group(function () {
    // Dashboard - redirect authenticated users to dashboard
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard.index');

    // Project management routes
    Route::resource('projects', ProjectsController::class)->except(['index']);
    Route::get('/projects/{project}/settings', [ProjectsController::class, 'settings'])->name('projects.settings');

    // Project routes
    Route::prefix('projects/{project}')->group(function () {
        // Links management
        Route::get('/links', [LinksController::class, 'index'])->name('links.index');
        Route::get('/links/create', [LinksController::class, 'create'])->name('links.create');
        Route::post('/links', [LinksController::class, 'store'])->name('links.store');
        Route::get('/links/{link}/edit', [LinksController::class, 'edit'])->name('links.edit');
        Route::put('/links/{link}', [LinksController::class, 'update'])->name('links.update');
        Route::delete('/links/{link}', [LinksController::class, 'destroy'])->name('links.destroy');

        // Analytics
        Route::get('/analytics', [AnalyticsController::class, 'index'])->name('analytics.index');
    });
});
