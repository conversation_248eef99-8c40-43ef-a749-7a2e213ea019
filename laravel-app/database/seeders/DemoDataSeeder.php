<?php

namespace Database\Seeders;

use App\Models\Analytics;
use App\Models\Domain;
use App\Models\Link;
use App\Models\Project;
use App\Models\Tag;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class DemoDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or get demo user
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'id' => Str::random(12),
                'name' => 'Demo User',
                'email_verified_at' => now(),
                'password_hash' => bcrypt('password'),
                'image' => 'https://avatar.vercel.sh/demo',
                'is_machine' => false,
                'source' => 'email',
                'default_workspace' => null,
            ]
        );

        // Create or get demo project
        $project = Project::firstOrCreate(
            ['slug' => 'acme-corp'],
            [
                'id' => Str::random(12),
                'name' => 'Acme Corp',
                'description' => 'Demo project for Acme Corporation with various marketing links',
                'plan' => 'pro',
                'user_id' => $user->id,
                'billing_cycle' => 'monthly',
                'billing_cycle_start' => 1,
                'usage_limit' => 1000,
                'usage' => 45,
                'links_usage' => 8,
                'links_limit' => 25,
                'domains_limit' => 10,
                'tags_limit' => 25,
                'users_limit' => 5,
                'invite_code' => Str::random(8),
            ]
        );

        // Create demo domains
        $primaryDomain = Domain::firstOrCreate(
            ['slug' => 'acme.link'],
            [
                'id' => Str::random(12),
                'verified' => true,
                'primary' => true,
                'archived' => false,
                'public_stats' => true,
                'target' => 'https://acme.com',
                'type' => 'redirect',
                'description' => 'Primary domain for Acme Corp short links',
                'project_id' => $project->id,
                'user_id' => $user->id,
            ]
        );

        $secondaryDomain = Domain::firstOrCreate(
            ['slug' => 'acme.co'],
            [
                'id' => Str::random(12),
                'verified' => true,
                'primary' => false,
                'archived' => false,
                'public_stats' => false,
                'target' => 'https://acme.com',
                'type' => 'redirect',
                'description' => 'Secondary domain for specific campaigns',
                'project_id' => $project->id,
                'user_id' => $user->id,
            ]
        );

        // Create demo tags
        $marketingTag = Tag::firstOrCreate(
            ['name' => 'Marketing', 'project_id' => $project->id],
            [
                'id' => Str::random(12),
                'color' => '#3B82F6',
                'description' => 'Marketing campaign links',
            ]
        );

        $socialTag = Tag::firstOrCreate(
            ['name' => 'Social Media', 'project_id' => $project->id],
            [
                'id' => Str::random(12),
                'color' => '#EC4899',
                'description' => 'Social media platform links',
            ]
        );

        $productTag = Tag::firstOrCreate(
            ['name' => 'Product', 'project_id' => $project->id],
            [
                'id' => Str::random(12),
                'color' => '#10B981',
                'description' => 'Product-related links',
            ]
        );

        // Create demo links
        $links = [
            [
                'id' => Str::random(12),
                'domain' => 'acme.link',
                'key' => 'homepage',
                'url' => 'https://acme.com',
                'short_link' => 'https://acme.link/homepage',
                'title' => 'Acme Corp Homepage',
                'description' => 'Main company website',
                'clicks' => 342,
                'last_clicked' => now()->subHours(2),
                'public_stats' => true,
                'tags' => [$marketingTag->id],
            ],
            [
                'id' => Str::random(12),
                'domain' => 'acme.link',
                'key' => 'signup',
                'url' => 'https://acme.com/signup',
                'short_link' => 'https://acme.link/signup',
                'title' => 'Sign Up for Acme',
                'description' => 'Registration page for new users',
                'clicks' => 156,
                'last_clicked' => now()->subMinutes(30),
                'public_stats' => false,
                'tags' => [$marketingTag->id, $productTag->id],
            ],
            [
                'id' => Str::random(12),
                'domain' => 'acme.link',
                'key' => 'twitter',
                'url' => 'https://twitter.com/acme',
                'short_link' => 'https://acme.link/twitter',
                'title' => 'Acme on Twitter',
                'description' => 'Follow us on Twitter for updates',
                'clicks' => 89,
                'last_clicked' => now()->subHours(5),
                'public_stats' => true,
                'tags' => [$socialTag->id],
            ],
            [
                'id' => Str::random(12),
                'domain' => 'acme.co',
                'key' => 'blog',
                'url' => 'https://acme.com/blog',
                'short_link' => 'https://acme.co/blog',
                'title' => 'Acme Blog',
                'description' => 'Latest news and updates from Acme',
                'clicks' => 67,
                'last_clicked' => now()->subDays(1),
                'public_stats' => true,
                'tags' => [$marketingTag->id],
            ],
            [
                'id' => Str::random(12),
                'domain' => 'acme.link',
                'key' => 'pricing',
                'url' => 'https://acme.com/pricing',
                'short_link' => 'https://acme.link/pricing',
                'title' => 'Acme Pricing',
                'description' => 'View our pricing plans',
                'clicks' => 234,
                'last_clicked' => now()->subHours(1),
                'public_stats' => true,
                'tags' => [$productTag->id],
            ],
        ];

        foreach ($links as $linkData) {
            $tagIds = $linkData['tags'];
            unset($linkData['tags']);

            $link = Link::firstOrCreate(
                ['key' => $linkData['key'], 'project_id' => $project->id],
                [
                    ...$linkData,
                    'user_id' => $user->id,
                    'tenant_id' => null,
                    'archived' => false,
                    'expires_at' => null,
                    'track_conversion' => true,
                    'proxy' => false,
                    'rewrite' => false,
                    'do_index' => true,
                    'leads' => 0,
                    'sales' => 0,
                    'sale_amount' => 0,
                ]
            );

            // Attach tags
            $link->tags()->sync($tagIds);

            // Create sample analytics data only if it's a new link
            if ($link->wasRecentlyCreated) {
                $this->createAnalyticsData($link, $user, $project);
            }
        }

        $this->command->info('Demo data created successfully!');
        $this->command->info('User: <EMAIL> / password');
        $this->command->info('Project: ' . $project->name . ' (' . $project->slug . ')');
    }

    private function createAnalyticsData(Link $link, User $user, Project $project): void
    {
        $countries = ['US', 'UK', 'CA', 'DE', 'FR', 'JP', 'AU'];
        $devices = ['Desktop', 'Mobile', 'Tablet'];
        $browsers = ['Chrome', 'Safari', 'Firefox', 'Edge'];
        $referers = ['google.com', 'twitter.com', 'facebook.com', 'linkedin.com', 'direct'];

        // Create analytics entries for the last 30 days
        for ($i = 0; $i < $link->clicks; $i++) {
            Analytics::create([
                'id' => Str::random(12),
                'link_id' => $link->id,
                'project_id' => $project->id,
                'user_id' => $user->id,
                'event' => 'click',
                'country' => $countries[array_rand($countries)],
                'device' => $devices[array_rand($devices)],
                'browser' => $browsers[array_rand($browsers)],
                'referer' => $referers[array_rand($referers)],
                'ip' => fake()->ipv4(),
                'ua' => fake()->userAgent(),
                'timestamp' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }

        // Add some conversion events (10% conversion rate)
        $conversions = (int) ($link->clicks * 0.1);
        for ($i = 0; $i < $conversions; $i++) {
            Analytics::create([
                'id' => Str::random(12),
                'link_id' => $link->id,
                'project_id' => $project->id,
                'user_id' => $user->id,
                'event' => 'conversion',
                'country' => $countries[array_rand($countries)],
                'device' => $devices[array_rand($devices)],
                'browser' => $browsers[array_rand($browsers)],
                'referer' => $referers[array_rand($referers)],
                'ip' => fake()->ipv4(),
                'ua' => fake()->userAgent(),
                'timestamp' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }
    }
}
