<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domains', function (Blueprint $table) {
            $table->string('id', 30)->primary(); // cuid
            $table->string('slug')->unique();
            $table->boolean('verified')->default(false);
            $table->boolean('primary')->default(false);
            $table->boolean('archived')->default(false);
            $table->boolean('public_stats')->default(false);
            $table->string('target')->nullable();
            $table->string('type')->default('redirect'); // redirect, rewrite
            $table->text('description')->nullable();
            $table->timestamp('registered_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->timestamp('created_at');
            $table->timestamp('updated_at');
            $table->string('project_id', 30);
            $table->string('user_id', 30);

            // Foreign key constraints
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes
            $table->index(['project_id', 'archived']);
            $table->index(['user_id', 'verified']);
            $table->index(['slug', 'verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};
