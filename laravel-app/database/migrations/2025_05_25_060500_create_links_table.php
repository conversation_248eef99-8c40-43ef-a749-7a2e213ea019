<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('links', function (Blueprint $table) {
            $table->string('id')->primary(); // cuid
            $table->string('domain'); // domain of the link (e.g. dub.sh)
            $table->string('key'); // key of the link (e.g. /github)
            $table->longText('url'); // target url
            $table->string('short_link', 400)->unique(); // full short link
            $table->boolean('archived')->default(false);
            $table->timestamp('expires_at')->nullable();
            $table->text('expired_url')->nullable();
            $table->string('password')->nullable();
            $table->boolean('track_conversion')->default(false);

            // OG tags and proxy
            $table->boolean('proxy')->default(false);
            $table->string('title')->nullable();
            $table->string('description', 280)->nullable();
            $table->longText('image')->nullable();
            $table->text('video')->nullable();

            // UTM parameters
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('utm_content')->nullable();

            // Link features
            $table->boolean('rewrite')->default(false); // link cloaking/masking
            $table->boolean('do_index')->default(false); // SEO indexing

            // Custom device targeting
            $table->text('ios')->nullable(); // custom link for iOS
            $table->text('android')->nullable(); // custom link for Android
            $table->json('geo')->nullable(); // custom link for specific countries

            // A/B Testing
            $table->json('test_variants')->nullable();
            $table->timestamp('test_started_at')->nullable();
            $table->timestamp('test_completed_at')->nullable();

            // Relations
            $table->string('user_id')->nullable();
            $table->string('project_id')->nullable();
            $table->string('program_id')->nullable();
            $table->string('folder_id')->nullable();
            $table->string('partner_id')->nullable();

            // External & tenant IDs
            $table->string('external_id')->nullable();
            $table->string('tenant_id')->nullable();

            // Stats
            $table->boolean('public_stats')->default(false);
            $table->integer('clicks')->default(0);
            $table->timestamp('last_clicked')->nullable();
            $table->integer('leads')->default(0);
            $table->integer('sales')->default(0);
            $table->integer('sale_amount')->default(0); // in cents

            $table->text('comments')->nullable();

            $table->timestamps();

            // Indexes
            $table->unique(['domain', 'key']);
            $table->unique(['project_id', 'external_id']);
            $table->index(['project_id', 'tenant_id']);
            $table->index(['project_id', 'folder_id', 'archived', 'created_at']);
            $table->index(['program_id', 'partner_id']);
            $table->index('folder_id');
            $table->index('user_id');

            // Foreign keys (will be added after other tables are created)
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('links');
    }
};
