<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('analytics', function (Blueprint $table) {
            $table->string('id', 30)->primary(); // cuid
            $table->string('link_id', 30);
            $table->string('project_id', 30);
            $table->string('user_id', 30);

            // Event tracking
            $table->string('event')->default('click'); // click, conversion, lead, sale
            $table->json('metadata')->nullable(); // Additional event data

            // Geographic data
            $table->string('country')->nullable();
            $table->string('city')->nullable();
            $table->string('region')->nullable();

            // Device and browser info
            $table->string('device')->nullable();
            $table->string('device_vendor')->nullable();
            $table->string('device_model')->nullable();
            $table->string('browser')->nullable();
            $table->string('browser_version')->nullable();
            $table->string('engine')->nullable();
            $table->string('engine_version')->nullable();
            $table->string('os')->nullable();
            $table->string('os_version')->nullable();
            $table->string('cpu_architecture')->nullable();
            $table->string('ua')->nullable(); // User agent

            // Request info
            $table->string('bot')->nullable();
            $table->string('referer')->nullable();
            $table->string('referer_url')->nullable();
            $table->string('ip')->nullable();
            $table->string('qr')->nullable();

            // Timestamps
            $table->timestamp('timestamp');
            $table->timestamp('created_at');

            // Foreign key constraints
            $table->foreign('link_id')->references('id')->on('links')->onDelete('cascade');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // Indexes for performance
            $table->index(['link_id', 'timestamp']);
            $table->index(['project_id', 'event', 'timestamp']);
            $table->index(['user_id', 'timestamp']);
            $table->index(['timestamp', 'event']);
            $table->index(['country', 'timestamp']);
            $table->index(['device', 'timestamp']);
            $table->index(['browser', 'timestamp']);
            $table->index(['referer', 'timestamp']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('analytics');
    }
};
