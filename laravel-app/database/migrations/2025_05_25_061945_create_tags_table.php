<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tags', function (Blueprint $table) {
            $table->string('id', 30)->primary(); // cuid
            $table->string('name');
            $table->string('color')->default('#8B5CF6'); // Default purple color
            $table->text('description')->nullable();
            $table->timestamp('created_at');
            $table->timestamp('updated_at');
            $table->string('project_id', 30);

            // Foreign key constraints
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('cascade');

            // Indexes
            $table->index(['project_id', 'name']);
            $table->unique(['project_id', 'name']); // Unique tag name per project
        });

        // Pivot table for link-tag relationships
        Schema::create('link_tag', function (Blueprint $table) {
            $table->string('link_id', 30);
            $table->string('tag_id', 30);
            $table->timestamp('created_at');

            // Foreign key constraints
            $table->foreign('link_id')->references('id')->on('links')->onDelete('cascade');
            $table->foreign('tag_id')->references('id')->on('tags')->onDelete('cascade');

            // Primary key
            $table->primary(['link_id', 'tag_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('link_tag');
        Schema::dropIfExists('tags');
    }
};
