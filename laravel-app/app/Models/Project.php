<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Project extends Model
{
    use HasFactory;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'name',
        'slug',
        'description',
        'logo',
        'user_id',
        'invite_code',
        'default_program_id',
        'plan',
        'stripe_id',
        'billing_cycle_start',
        'payment_failed_at',
        'invoice_prefix',
        'stripe_connect_id',
        'shopify_store_id',
        'total_links',
        'total_clicks',
        'usage',
        'usage_limit',
        'usage_count',
        'links_usage',
        'links_limit',
        'sales_usage',
        'sales_limit',
        'domains_limit',
        'tags_limit',
        'folders_usage',
        'folders_limit',
        'users_limit',
        'ai_usage',
        'ai_limit',
        'referral_link_id',
        'referred_signups',
        'store',
        'allowed_hostnames',
        'conversion_enabled',
        'webhook_enabled',
        'partners_enabled',
        'sso_enabled',
        'dot_link_claimed',
        'usage_last_checked',
    ];

    protected $casts = [
        'payment_failed_at' => 'datetime',
        'usage_last_checked' => 'datetime',
        'store' => 'array',
        'allowed_hostnames' => 'array',
        'conversion_enabled' => 'boolean',
        'webhook_enabled' => 'boolean',
        'partners_enabled' => 'boolean',
        'sso_enabled' => 'boolean',
        'dot_link_claimed' => 'boolean',
        'billing_cycle_start' => 'integer',
        'total_links' => 'integer',
        'total_clicks' => 'integer',
        'usage' => 'integer',
        'usage_limit' => 'integer',
        'usage_count' => 'integer',
        'links_usage' => 'integer',
        'links_limit' => 'integer',
        'sales_usage' => 'integer',
        'sales_limit' => 'integer',
        'domains_limit' => 'integer',
        'tags_limit' => 'integer',
        'folders_usage' => 'integer',
        'folders_limit' => 'integer',
        'users_limit' => 'integer',
        'ai_usage' => 'integer',
        'ai_limit' => 'integer',
        'referred_signups' => 'integer',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_users')
            ->withPivot(['role', 'created_at'])
            ->withTimestamps();
    }

    public function links(): HasMany
    {
        return $this->hasMany(Link::class);
    }

    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class);
    }

    public function tags(): HasMany
    {
        return $this->hasMany(Tag::class);
    }

    public function analytics(): HasMany
    {
        return $this->hasMany(Analytics::class);
    }

    // Scopes for reusability and performance
    public function scopeActive($query)
    {
        return $query->where('plan', '!=', 'cancelled');
    }

    public function scopeByPlan($query, string $plan)
    {
        return $query->where('plan', $plan);
    }

    public function scopeWithUsageLimits($query)
    {
        return $query->select([
            'id', 'name', 'slug', 'plan',
            'usage', 'usage_limit',
            'links_usage', 'links_limit',
            'sales_usage', 'sales_limit'
        ]);
    }

    public function scopeExceedsUsage($query)
    {
        return $query->whereColumn('usage', '>', 'usage_limit');
    }

    public function scopeNeedsUsageCheck($query)
    {
        return $query->where('usage_last_checked', '<', now()->subHour());
    }

    // Helper methods
    public function isWithinUsageLimit(): bool
    {
        return $this->usage <= $this->usage_limit;
    }

    public function isWithinLinksLimit(): bool
    {
        return $this->links_usage <= $this->links_limit;
    }

    public function canCreateLinks(): bool
    {
        return $this->isWithinLinksLimit();
    }

    public function incrementUsage(int $amount = 1): void
    {
        $this->increment('usage', $amount);
        $this->update(['usage_last_checked' => now()]);
    }

    public function incrementLinksUsage(int $amount = 1): void
    {
        $this->increment('links_usage', $amount);
    }
}
