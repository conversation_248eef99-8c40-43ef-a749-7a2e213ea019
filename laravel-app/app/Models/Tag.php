<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;

class Tag extends Model
{
    protected $fillable = [
        'id',
        'name',
        'color',
        'description',
        'project_id',
    ];

    public $incrementing = false;
    protected $keyType = 'string';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = Str::random(12);
            }
        });
    }

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function links(): BelongsToMany
    {
        return $this->belongsToMany(Link::class, 'link_tag')
            ->withTimestamps(['created_at']);
    }

    // Scopes
    public function scopeForProject($query, string $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeWithOptimizedSelect($query)
    {
        return $query->select([
            'id',
            'name',
            'color',
            'description',
            'project_id',
            'created_at',
            'updated_at',
        ]);
    }

    public function scopeWithLinksCount($query)
    {
        return $query->withCount('links');
    }

    // Helper methods
    public function getLinksCount(): int
    {
        return $this->links()->count();
    }
}
