<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Str;

class User extends Authenticatable
{
    use HasFactory, Notifiable, HasApiTokens;

    public $incrementing = false;
    protected $keyType = 'string';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = (string) Str::uuid();
            }
        });
    }

    protected $fillable = [
        'id',
        'name',
        'email',
        'email_verified_at',
        'image',
        'is_machine',
        'password_hash',
        'invalid_login_attempts',
        'locked_at',
        'subscribed',
        'source',
        'default_workspace',
        'default_partner_id',
    ];

    protected $hidden = [
        'password_hash',
        'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'locked_at' => 'datetime',
        'is_machine' => 'boolean',
        'subscribed' => 'boolean',
        'invalid_login_attempts' => 'integer',
    ];

    /**
     * Get the password for the user.
     */
    public function getAuthPassword()
    {
        return $this->password_hash;
    }

    // Relationships
    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_users')
            ->withPivot(['role', 'created_at'])
            ->withTimestamps();
    }

    public function links(): HasMany
    {
        return $this->hasMany(Link::class);
    }

    // Scopes for reusability and performance
    public function scopeActive($query)
    {
        return $query->whereNull('locked_at');
    }

    public function scopeSubscribed($query)
    {
        return $query->where('subscribed', true);
    }

    public function scopeBySource($query, string $source)
    {
        return $query->where('source', $source);
    }

    public function scopeWithWorkspace($query, string $workspace)
    {
        return $query->where('default_workspace', $workspace);
    }

    // Performance optimized queries with select()
    public function scopeBasicInfo($query)
    {
        return $query->select(['id', 'name', 'email', 'image']);
    }

    // Helper methods
    public function isLocked(): bool
    {
        return !is_null($this->locked_at);
    }

    public function lockAccount(): void
    {
        $this->update(['locked_at' => now()]);
    }

    public function unlockAccount(): void
    {
        $this->update([
            'locked_at' => null,
            'invalid_login_attempts' => 0,
        ]);
    }
}
