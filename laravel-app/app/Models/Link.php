<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Link extends Model
{
    use HasFactory;

    public $incrementing = false;
    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'domain',
        'key',
        'url',
        'short_link',
        'archived',
        'expires_at',
        'expired_url',
        'password',
        'track_conversion',
        'proxy',
        'title',
        'description',
        'image',
        'video',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'rewrite',
        'do_index',
        'ios',
        'android',
        'geo',
        'test_variants',
        'test_started_at',
        'test_completed_at',
        'user_id',
        'project_id',
        'program_id',
        'folder_id',
        'partner_id',
        'external_id',
        'tenant_id',
        'public_stats',
        'clicks',
        'last_clicked',
        'leads',
        'sales',
        'sale_amount',
        'comments',
    ];

    protected $casts = [
        'archived' => 'boolean',
        'expires_at' => 'datetime',
        'track_conversion' => 'boolean',
        'proxy' => 'boolean',
        'rewrite' => 'boolean',
        'do_index' => 'boolean',
        'geo' => 'array',
        'test_variants' => 'array',
        'test_started_at' => 'datetime',
        'test_completed_at' => 'datetime',
        'public_stats' => 'boolean',
        'clicks' => 'integer',
        'last_clicked' => 'datetime',
        'leads' => 'integer',
        'sales' => 'integer',
        'sale_amount' => 'integer',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function folder(): BelongsTo
    {
        return $this->belongsTo(Folder::class);
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, 'link_tag')
            ->withTimestamps();
    }

    // Scopes for reusability and performance
    public function scopeActive($query)
    {
        return $query->where('archived', false);
    }

    public function scopeArchived($query)
    {
        return $query->where('archived', true);
    }

    public function scopeNotExpired($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    public function scopePublicStats($query)
    {
        return $query->where('public_stats', true);
    }

    public function scopeByDomain($query, string $domain)
    {
        return $query->where('domain', $domain);
    }

    public function scopeByProject($query, string $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeByFolder($query, string $folderId)
    {
        return $query->where('folder_id', $folderId);
    }

    // Performance optimized scopes with select()
    public function scopeBasicInfo($query)
    {
        return $query->select([
            'id', 'domain', 'key', 'url', 'short_link',
            'title', 'clicks', 'created_at'
        ]);
    }

    public function scopeStats($query)
    {
        return $query->select([
            'id', 'clicks', 'leads', 'sales', 'sale_amount',
            'last_clicked', 'public_stats'
        ]);
    }

    public function scopePopular($query, int $minClicks = 100)
    {
        return $query->where('clicks', '>=', $minClicks)
                    ->orderBy('clicks', 'desc');
    }

    public function scopeRecentlyClicked($query, int $days = 7)
    {
        return $query->where('last_clicked', '>=', now()->subDays($days));
    }

    // Helper methods
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    public function isArchived(): bool
    {
        return $this->archived;
    }

    public function isActive(): bool
    {
        return !$this->isArchived() && !$this->isExpired();
    }

    public function incrementClicks(): void
    {
        $this->increment('clicks');
        $this->update(['last_clicked' => now()]);
    }

    public function getFullUrlAttribute(): string
    {
        return "https://{$this->domain}/{$this->key}";
    }

    public function hasPassword(): bool
    {
        return !empty($this->password);
    }

    public function hasCustomDeviceTargeting(): bool
    {
        return !empty($this->ios) || !empty($this->android) || !empty($this->geo);
    }
}
