<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Domain extends Model
{
    protected $fillable = [
        'id',
        'slug',
        'verified',
        'primary',
        'archived',
        'public_stats',
        'target',
        'type',
        'description',
        'registered_at',
        'expires_at',
        'project_id',
        'user_id',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'primary' => 'boolean',
        'archived' => 'boolean',
        'public_stats' => 'boolean',
        'registered_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    public $incrementing = false;
    protected $keyType = 'string';

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = Str::random(12);
            }
        });
    }

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function links(): HasMany
    {
        return $this->hasMany(Link::class);
    }

    // Scopes
    public function scopeVerified($query)
    {
        return $query->where('verified', true);
    }

    public function scopeUnarchived($query)
    {
        return $query->where('archived', false);
    }

    public function scopePrimary($query)
    {
        return $query->where('primary', true);
    }

    public function scopeForProject($query, string $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeWithOptimizedSelect($query)
    {
        return $query->select([
            'id',
            'slug',
            'verified',
            'primary',
            'archived',
            'public_stats',
            'target',
            'type',
            'description',
            'registered_at',
            'expires_at',
            'project_id',
            'user_id',
            'created_at',
            'updated_at',
        ]);
    }

    // Helper methods
    public function isVerified(): bool
    {
        return $this->verified;
    }

    public function isPrimary(): bool
    {
        return $this->primary;
    }

    public function isArchived(): bool
    {
        return $this->archived;
    }

    public function getFullUrl(): string
    {
        return "https://{$this->slug}";
    }
}
