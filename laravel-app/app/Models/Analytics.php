<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Analytics extends Model
{
    protected $fillable = [
        'id',
        'link_id',
        'project_id',
        'user_id',
        'event',
        'metadata',
        'country',
        'city',
        'region',
        'device',
        'device_vendor',
        'device_model',
        'browser',
        'browser_version',
        'engine',
        'engine_version',
        'os',
        'os_version',
        'cpu_architecture',
        'ua',
        'bot',
        'referer',
        'referer_url',
        'ip',
        'qr',
        'timestamp',
    ];

    protected $casts = [
        'metadata' => 'array',
        'timestamp' => 'datetime',
    ];

    public $incrementing = false;
    protected $keyType = 'string';
    public $timestamps = false; // We only use created_at

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->id)) {
                $model->id = Str::random(12);
            }
            if (empty($model->timestamp)) {
                $model->timestamp = now();
            }
            $model->created_at = now();
        });
    }

    // Relationships
    public function link(): BelongsTo
    {
        return $this->belongsTo(Link::class);
    }

    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Scopes for analytics queries
    public function scopeForLink($query, string $linkId)
    {
        return $query->where('link_id', $linkId);
    }

    public function scopeForProject($query, string $projectId)
    {
        return $query->where('project_id', $projectId);
    }

    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeByEvent($query, string $event)
    {
        return $query->where('event', $event);
    }

    public function scopeByCountry($query, string $country)
    {
        return $query->where('country', $country);
    }

    public function scopeByDevice($query, string $device)
    {
        return $query->where('device', $device);
    }

    public function scopeByBrowser($query, string $browser)
    {
        return $query->where('browser', $browser);
    }

    public function scopeByReferer($query, string $referer)
    {
        return $query->where('referer', $referer);
    }

    public function scopeInDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('timestamp', [$startDate, $endDate]);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('timestamp', today());
    }

    public function scopeYesterday($query)
    {
        return $query->whereDate('timestamp', today()->subDay());
    }

    public function scopeThisWeek($query)
    {
        return $query->whereBetween('timestamp', [now()->startOfWeek(), now()->endOfWeek()]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereBetween('timestamp', [now()->startOfMonth(), now()->endOfMonth()]);
    }

    public function scopeLastDays($query, int $days)
    {
        return $query->where('timestamp', '>=', now()->subDays($days));
    }

    // Analytics aggregation scopes
    public function scopeGroupByCountry($query)
    {
        return $query->selectRaw('country, COUNT(*) as count')
            ->groupBy('country')
            ->orderByDesc('count');
    }

    public function scopeGroupByDevice($query)
    {
        return $query->selectRaw('device, COUNT(*) as count')
            ->groupBy('device')
            ->orderByDesc('count');
    }

    public function scopeGroupByBrowser($query)
    {
        return $query->selectRaw('browser, COUNT(*) as count')
            ->groupBy('browser')
            ->orderByDesc('count');
    }

    public function scopeGroupByReferer($query)
    {
        return $query->selectRaw('referer, COUNT(*) as count')
            ->groupBy('referer')
            ->orderByDesc('count');
    }

    public function scopeGroupByDate($query, string $format = '%Y-%m-%d')
    {
        return $query->selectRaw("DATE_FORMAT(timestamp, '{$format}') as date, COUNT(*) as count")
            ->groupBy('date')
            ->orderBy('date');
    }
}
