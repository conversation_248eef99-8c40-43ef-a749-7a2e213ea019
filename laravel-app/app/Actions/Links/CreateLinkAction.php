<?php

namespace App\Actions\Links;

use App\Models\Link;
use App\Models\Project;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class CreateLinkAction
{
    public function execute(Project $project, array $data): Link
    {
        // Validate project limits
        if (!$project->canCreateLinks()) {
            throw ValidationException::withMessages([
                'links' => 'Link limit exceeded for this project.'
            ]);
        }

        // Generate short link ID
        $data['id'] = $this->generateLinkId();

        // Generate short link if not provided
        if (empty($data['short_link'])) {
            $data['short_link'] = $this->generateShortLink($data['domain'], $data['key']);
        }

        // Set project relationship
        $data['project_id'] = $project->id;

        // Create the link using Eloquent (not query builder)
        $link = Link::create($data);

        // Update project usage
        $project->incrementLinksUsage();

        return $link->load(['project:id,name,slug', 'user:id,name,email']);
    }

    private function generateLinkId(): string
    {
        do {
            $id = Str::random(12); // Similar to cuid
        } while (Link::where('id', $id)->exists());

        return $id;
    }

    private function generateShortLink(string $domain, string $key): string
    {
        $key = ltrim($key, '/');
        return "https://{$domain}/{$key}";
    }

    private function ensureUniqueKey(string $domain, string $key): string
    {
        $originalKey = $key;
        $counter = 1;

        while (Link::where('domain', $domain)->where('key', $key)->exists()) {
            $key = $originalKey . '-' . $counter;
            $counter++;
        }

        return $key;
    }
}
