<?php

namespace App\Actions\Links;

use App\Models\Link;
use App\Models\Project;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;

class GetLinksAction
{
    public function execute(Project $project, array $filters = []): LengthAwarePaginator
    {
        return Link::query()
            ->select([
                'id', 'domain', 'key', 'url', 'short_link',
                'title', 'description', 'image', 'archived',
                'clicks', 'leads', 'sales', 'last_clicked',
                'public_stats', 'created_at', 'project_id',
                'folder_id', 'user_id'
            ])
            ->byProject($project->id)
            ->with([
                'project:id,name,slug',
                'user:id,name,email,image',
                'folder:id,name'
            ])
            ->when(isset($filters['archived']), function (Builder $query) use ($filters) {
                return $filters['archived'] ? $query->archived() : $query->active();
            })
            ->when(isset($filters['domain']), function (Builder $query) use ($filters) {
                return $query->byDomain($filters['domain']);
            })
            ->when(isset($filters['folder_id']), function (Builder $query) use ($filters) {
                return $query->byFolder($filters['folder_id']);
            })
            ->when(isset($filters['search']), function (Builder $query) use ($filters) {
                return $query->where(function ($q) use ($filters) {
                    $q->where('key', 'like', "%{$filters['search']}%")
                      ->orWhere('url', 'like', "%{$filters['search']}%")
                      ->orWhere('title', 'like', "%{$filters['search']}%");
                });
            })
            ->when(isset($filters['tag']), function (Builder $query) use ($filters) {
                return $query->whereHas('tags', function ($q) use ($filters) {
                    $q->where('name', $filters['tag']);
                });
            })
            ->orderBy($filters['sort'] ?? 'created_at', $filters['order'] ?? 'desc')
            ->paginate($filters['per_page'] ?? 25);
    }

    public function getPopularLinks(Project $project, int $limit = 10): array
    {
        return Link::byProject($project->id)
            ->active()
            ->popular(10) // minimum 10 clicks
            ->basicInfo()
            ->limit($limit)
            ->get()
            ->toArray();
    }

    public function getRecentlyClickedLinks(Project $project, int $days = 7): array
    {
        return Link::byProject($project->id)
            ->active()
            ->recentlyClicked($days)
            ->basicInfo()
            ->orderBy('last_clicked', 'desc')
            ->limit(20)
            ->get()
            ->toArray();
    }

    public function getLinkStats(Project $project): array
    {
        $stats = Link::byProject($project->id)
            ->selectRaw('
                COUNT(*) as total_links,
                COUNT(CASE WHEN archived = 0 THEN 1 END) as active_links,
                COUNT(CASE WHEN archived = 1 THEN 1 END) as archived_links,
                SUM(clicks) as total_clicks,
                SUM(leads) as total_leads,
                SUM(sales) as total_sales,
                SUM(sale_amount) as total_revenue
            ')
            ->first();

        return [
            'total_links' => $stats->total_links ?? 0,
            'active_links' => $stats->active_links ?? 0,
            'archived_links' => $stats->archived_links ?? 0,
            'total_clicks' => $stats->total_clicks ?? 0,
            'total_leads' => $stats->total_leads ?? 0,
            'total_sales' => $stats->total_sales ?? 0,
            'total_revenue' => $stats->total_revenue ?? 0,
        ];
    }

    public function findByShortLink(string $domain, string $key): ?Link
    {
        return Link::where('domain', $domain)
            ->where('key', $key)
            ->active()
            ->notExpired()
            ->first();
    }

    public function findByExternalId(Project $project, string $externalId): ?Link
    {
        return Link::byProject($project->id)
            ->where('external_id', $externalId)
            ->first();
    }
}
