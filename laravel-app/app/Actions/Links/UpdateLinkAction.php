<?php

namespace App\Actions\Links;

use App\Models\Link;

class UpdateLinkAction
{
    public function execute(Link $link, array $data): Link
    {
        // Filter out non-updatable fields
        $allowedFields = [
            'url', 'title', 'description', 'image', 'video',
            'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
            'expires_at', 'expired_url', 'password', 'proxy', 'rewrite',
            'do_index', 'ios', 'android', 'geo', 'comments',
            'track_conversion', 'public_stats'
        ];

        $updateData = array_intersect_key($data, array_flip($allowedFields));

        // Update using Eloquent
        $link->update($updateData);

        // Return with optimized select and relationships
        return $link->fresh(['project:id,name,slug', 'user:id,name,email']);
    }

    public function archive(Link $link): Link
    {
        $link->update(['archived' => true]);

        return $link;
    }

    public function unarchive(Link $link): Link
    {
        $link->update(['archived' => false]);

        return $link;
    }

    public function incrementClicks(Link $link): void
    {
        // Use Eloquent increment method for atomic operation
        $link->increment('clicks');
        $link->update(['last_clicked' => now()]);
    }

    public function updateStats(Link $link, array $stats): Link
    {
        $allowedStats = ['clicks', 'leads', 'sales', 'sale_amount'];
        $updateData = array_intersect_key($stats, array_flip($allowedStats));

        $link->update($updateData);

        return $link;
    }
}
