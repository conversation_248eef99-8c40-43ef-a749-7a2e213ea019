<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\User;

class CheckAuthSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:check-setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check if authentication system is properly set up';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Checking Authentication System Setup...');
        $this->newLine();

        // Check database connection
        try {
            DB::connection()->getPdo();
            $this->info('✅ Database connection: OK');
        } catch (\Exception $e) {
            $this->error('❌ Database connection: FAILED');
            $this->error('Error: ' . $e->getMessage());
            return;
        }

        // Check if users table exists and has correct structure
        try {
            $columns = DB::getSchemaBuilder()->getColumnListing('users');
            $requiredColumns = ['id', 'name', 'email', 'password_hash', 'email_verified_at'];

            $missingColumns = array_diff($requiredColumns, $columns);

            if (empty($missingColumns)) {
                $this->info('✅ Users table structure: OK');
            } else {
                $this->error('❌ Users table missing columns: ' . implode(', ', $missingColumns));
            }
        } catch (\Exception $e) {
            $this->error('❌ Users table check: FAILED');
            $this->error('Error: ' . $e->getMessage());
        }

        // Check if we can create a test user
        try {
            $testUser = User::factory()->make();
            $this->info('✅ User factory: OK');
        } catch (\Exception $e) {
            $this->error('❌ User factory: FAILED');
            $this->error('Error: ' . $e->getMessage());
        }

        // Check user count
        try {
            $userCount = User::count();
            $this->info("✅ Users in database: {$userCount}");
        } catch (\Exception $e) {
            $this->error('❌ User count check: FAILED');
            $this->error('Error: ' . $e->getMessage());
        }

        // Check if auth configuration is working
        try {
            $authModel = config('auth.providers.users.model');
            $this->info("✅ Auth model configured: {$authModel}");

            $guardDriver = config('auth.defaults.guard');
            $this->info("✅ Default guard: {$guardDriver}");
        } catch (\Exception $e) {
            $this->error('❌ Auth configuration: FAILED');
            $this->error('Error: ' . $e->getMessage());
        }

        $this->newLine();
        $this->info('🎉 Authentication system check completed!');

        // Show sample user for testing
        if (User::count() > 0) {
            $sampleUser = User::first();
            $this->newLine();
            $this->info('📝 Test User Credentials:');
            $this->line("Email: {$sampleUser->email}");
            $this->line('Password: password');
        }
    }
}
