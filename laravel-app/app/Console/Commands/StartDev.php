<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Symfony\Component\Process\Process;

class StartDev extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'dev:start';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Start Laravel and Vite development servers';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🚀 Starting Development Servers...');
        $this->newLine();

        // Check if npm dependencies are installed
        if (!file_exists(base_path('node_modules'))) {
            $this->warn('📦 Installing npm dependencies...');
            $npmInstall = new Process(['npm', 'install'], base_path());
            $npmInstall->setTimeout(300); // 5 minutes
            $npmInstall->run();

            if (!$npmInstall->isSuccessful()) {
                $this->error('❌ Failed to install npm dependencies');
                return;
            }
            $this->info('✅ npm dependencies installed');
        }

        $this->info('🔧 Starting Laravel server on http://localhost:8000...');
        $this->info('🔧 Starting Vite server on http://localhost:5173...');
        $this->newLine();

        $this->warn('Press Ctrl+C to stop both servers');
        $this->newLine();

        // Start Laravel server
        $laravelProcess = new Process(['php', 'artisan', 'serve'], base_path());
        $laravelProcess->start();

        // Start Vite server
        $viteProcess = new Process(['npm', 'run', 'dev'], base_path());
        $viteProcess->start();

        // Keep processes running and handle output
        while ($laravelProcess->isRunning() || $viteProcess->isRunning()) {
            // Output Laravel server logs
            while ($laravelProcess->isRunning() && !$laravelProcess->getIncrementalOutput() === '') {
                $output = $laravelProcess->getIncrementalOutput();
                if (!empty($output)) {
                    $this->line('[Laravel] ' . trim($output));
                }
            }

            // Output Vite server logs
            while ($viteProcess->isRunning() && !$viteProcess->getIncrementalOutput() === '') {
                $output = $viteProcess->getIncrementalOutput();
                if (!empty($output)) {
                    $this->line('[Vite] ' . trim($output));
                }
            }

            usleep(100000); // Sleep for 0.1 seconds
        }

        $this->info('🛑 Development servers stopped');
    }
}
