<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class TestAuthFlow extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'auth:test-flow';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the complete authentication flow';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🧪 Testing Complete Authentication Flow...');
        $this->newLine();

        // Test 1: Create a new user
        $this->info('1️⃣ Testing User Creation...');
        try {
            $testUser = User::create([
                'name' => 'Test User ' . now()->format('His'),
                'email' => 'test' . now()->format('His') . '@example.com',
                'password_hash' => Hash::make('testpassword'),
                'email_verified_at' => now(),
            ]);

            $this->info("   ✅ User created: {$testUser->email} (ID: {$testUser->id})");
        } catch (\Exception $e) {
            $this->error("   ❌ User creation failed: {$e->getMessage()}");
            return;
        }

        // Test 2: Test password verification
        $this->info('2️⃣ Testing Password Verification...');
        if (Hash::check('testpassword', $testUser->password_hash)) {
            $this->info('   ✅ Password verification: PASSED');
        } else {
            $this->error('   ❌ Password verification: FAILED');
        }

        // Test 3: Test authentication
        $this->info('3️⃣ Testing Authentication...');
        try {
            Auth::login($testUser);
            if (Auth::check() && Auth::id() === $testUser->id) {
                $this->info('   ✅ User authentication: PASSED');
                $this->info("   ✅ Authenticated user: {" . Auth::user()->name . "}");
            } else {
                $this->error('   ❌ User authentication: FAILED');
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Authentication failed: {$e->getMessage()}");
        }

        // Test 4: Test logout
        $this->info('4️⃣ Testing Logout...');
        Auth::logout();
        if (!Auth::check()) {
            $this->info('   ✅ User logout: PASSED');
        } else {
            $this->error('   ❌ User logout: FAILED');
        }

        // Test 5: Test getAuthPassword method
        $this->info('5️⃣ Testing Custom Auth Password Method...');
        $authPassword = $testUser->getAuthPassword();
        if ($authPassword === $testUser->password_hash) {
            $this->info('   ✅ getAuthPassword method: PASSED');
        } else {
            $this->error('   ❌ getAuthPassword method: FAILED');
        }

        // Test 6: Cleanup
        $this->info('6️⃣ Cleaning up test data...');
        $testUser->delete();
        $this->info('   ✅ Test user deleted');

        $this->newLine();
        $this->info('🎉 Authentication Flow Test Completed Successfully!');
        $this->newLine();

        // Show current user count
        $userCount = User::count();
        $this->info("📊 Total users in database: {$userCount}");

        if ($userCount > 0) {
            $this->info('📋 Available test accounts:');
            User::take(3)->get(['email'])->each(function ($user) {
                $this->line("   • {$user->email} (password: password)");
            });
        }
    }
}
