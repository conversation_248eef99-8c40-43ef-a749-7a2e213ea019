<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Inertia\Inertia;
use Inertia\Response;

class ProjectsController extends Controller
{
    public function index(): Response
    {
        // For demo purposes, use sample user
        $user = User::first();

        $projects = Project::where('user_id', $user->id ?? 1)
            ->select(['id', 'name', 'slug', 'description', 'plan', 'created_at'])
            ->withCount(['links', 'domains'])
            ->with(['analytics' => function ($query) {
                $query->selectRaw('project_id, SUM(clicks) as total_clicks')
                    ->groupBy('project_id');
            }])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'name' => $project->name,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'plan' => $project->plan,
                    'total_links' => $project->links_count,
                    'total_clicks' => $project->analytics->sum('total_clicks') ?? 0,
                    'created_at' => $project->created_at->toISOString(),
                ];
            });

        $stats = [
            'total_projects' => $projects->count(),
            'total_links' => $projects->sum('total_links'),
            'total_clicks' => $projects->sum('total_clicks'),
            'click_growth' => 12.5, // Sample growth percentage
        ];

        return Inertia::render('Dashboard', [
            'projects' => $projects,
            'user' => [
                'id' => $user->id ?? 'user_123',
                'name' => $user->name ?? 'Demo User',
                'email' => $user->email ?? '<EMAIL>',
                'image' => $user->image ?? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
            ],
            'stats' => $stats,
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('Projects/Create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'plan' => 'in:free,pro,business',
        ]);

        // Generate unique slug
        $slug = Str::slug($validated['name']);
        $originalSlug = $slug;
        $counter = 1;

        while (Project::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $user = User::first(); // For demo purposes

        $project = Project::create([
            'name' => $validated['name'],
            'slug' => $slug,
            'description' => $validated['description'] ?? null,
            'plan' => $validated['plan'] ?? 'free',
            'user_id' => $user->id ?? 1,
            'usage_limit' => $this->getUsageLimit($validated['plan'] ?? 'free'),
            'links_limit' => $this->getLinksLimit($validated['plan'] ?? 'free'),
            'domains_limit' => $this->getDomainsLimit($validated['plan'] ?? 'free'),
            'tags_limit' => $this->getTagsLimit($validated['plan'] ?? 'free'),
            'users_limit' => $this->getUsersLimit($validated['plan'] ?? 'free'),
        ]);

        return redirect()->route('projects.settings', $project->slug)
            ->with('success', 'Project created successfully!');
    }

    public function show(Project $project): Response
    {
        $project->load(['domains', 'tags']);

        $stats = [
            'links_count' => $project->links()->count(),
            'clicks_count' => $project->analytics()->sum('clicks'),
            'domains_count' => $project->domains()->count(),
            'tags_count' => $project->tags()->count(),
        ];

        return Inertia::render('Projects/Show', [
            'project' => array_merge($project->toArray(), $stats),
        ]);
    }

    public function edit(Project $project): Response
    {
        return Inertia::render('Projects/Edit', [
            'project' => $project,
        ]);
    }

    public function update(Request $request, Project $project)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        // Update slug if name changed
        if ($validated['name'] !== $project->name) {
            $slug = Str::slug($validated['name']);
            $originalSlug = $slug;
            $counter = 1;

            while (Project::where('slug', $slug)->where('id', '!=', $project->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            $validated['slug'] = $slug;
        }

        $project->update($validated);

        return back()->with('success', 'Project updated successfully!');
    }

    public function destroy(Project $project)
    {
        // Delete related data
        $project->links()->delete();
        $project->analytics()->delete();
        $project->domains()->delete();
        $project->tags()->delete();

        $project->delete();

        return redirect()->route('dashboard')
            ->with('success', 'Project deleted successfully!');
    }

    public function settings(Project $project): Response
    {
        return Inertia::render('Projects/Settings', [
            'project' => [
                'id' => $project->id,
                'name' => $project->name,
                'slug' => $project->slug,
                'description' => $project->description,
                'plan' => $project->plan,
                'usage_limit' => $project->usage_limit,
                'usage_count' => $project->usage_count,
                'links_usage' => $project->links()->count(),
                'links_limit' => $project->links_limit,
                'domains_limit' => $project->domains_limit,
                'tags_limit' => $project->tags_limit,
                'users_limit' => $project->users_limit,
            ],
        ]);
    }

    private function getUsageLimit(string $plan): int
    {
        return match ($plan) {
            'free' => 1000,
            'pro' => 25000,
            'business' => 150000,
            default => 1000,
        };
    }

    private function getLinksLimit(string $plan): int
    {
        return match ($plan) {
            'free' => 25,
            'pro' => 1000,
            'business' => 5000,
            default => 25,
        };
    }

    private function getDomainsLimit(string $plan): int
    {
        return match ($plan) {
            'free' => 3,
            'pro' => 10,
            'business' => 40,
            default => 3,
        };
    }

    private function getTagsLimit(string $plan): int
    {
        return match ($plan) {
            'free' => 5,
            'pro' => 25,
            'business' => 150,
            default => 5,
        };
    }

    private function getUsersLimit(string $plan): int
    {
        return match ($plan) {
            'free' => 1,
            'pro' => 5,
            'business' => 25,
            default => 1,
        };
    }
}
