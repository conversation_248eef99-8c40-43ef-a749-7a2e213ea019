<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Link;
use Illuminate\Http\Request;
use Inertia\Inertia;

class LinksController extends Controller
{
    public function index(Request $request, Project $project)
    {
        $links = $project->links()
            ->with('tags')
            ->orderBy('created_at', 'desc')
            ->paginate(25);

        return Inertia::render('Links/Index', [
            'links' => $links,
            'project' => $project->only(['id', 'name', 'slug']),
        ]);
    }

    public function create(Project $project)
    {
        $domains = $project->domains()->where('verified', true)->get();
        $tags = $project->tags()->get();

        return Inertia::render('Links/Create', [
            'project' => $project->only(['id', 'name', 'slug']),
            'domains' => $domains,
            'tags' => $tags,
        ]);
    }

    public function edit(Project $project, Link $link)
    {
        $link->load('tags');
        $domains = $project->domains()->where('verified', true)->get();
        $tags = $project->tags()->get();

        return Inertia::render('Links/Edit', [
            'project' => $project->only(['id', 'name', 'slug']),
            'link' => $link,
            'domains' => $domains,
            'tags' => $tags,
        ]);
    }

    public function store(Request $request, Project $project)
    {
        $validated = $request->validate([
            'url' => 'required|url',
            'key' => 'nullable|string|max:255|unique:links,key',
            'domain' => 'required|string|exists:domains,slug',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
        ]);

        // Generate key if not provided
        if (empty($validated['key'])) {
            $validated['key'] = $this->generateUniqueKey();
        }

        // Get domain
        $domain = $project->domains()->where('slug', $validated['domain'])->firstOrFail();

        // Create link
        $link = $project->links()->create([
            'url' => $validated['url'],
            'key' => $validated['key'],
            'domain_id' => $domain->id,
            'title' => $validated['title'],
            'description' => $validated['description'],
            'user_id' => auth()->id(),
        ]);

        // Attach tags
        if (!empty($validated['tags'])) {
            $link->tags()->sync($validated['tags']);
        }

        return redirect()->route('links.index', $project->slug)
            ->with('success', 'Link created successfully.');
    }

    public function update(Request $request, Project $project, Link $link)
    {
        $validated = $request->validate([
            'url' => 'required|url',
            'key' => 'required|string|max:255|unique:links,key,' . $link->id,
            'domain' => 'required|string|exists:domains,slug',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:500',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
        ]);

        // Get domain
        $domain = $project->domains()->where('slug', $validated['domain'])->firstOrFail();

        // Update link
        $link->update([
            'url' => $validated['url'],
            'key' => $validated['key'],
            'domain_id' => $domain->id,
            'title' => $validated['title'],
            'description' => $validated['description'],
        ]);

        // Sync tags
        $link->tags()->sync($validated['tags'] ?? []);

        return redirect()->route('links.index', $project->slug)
            ->with('success', 'Link updated successfully.');
    }

    public function destroy(Project $project, Link $link)
    {
        $link->delete();

        return redirect()->route('links.index', $project->slug)
            ->with('success', 'Link deleted successfully.');
    }

    private function generateUniqueKey($length = 6)
    {
        $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';

        do {
            $key = '';
            for ($i = 0; $i < $length; $i++) {
                $key .= $characters[random_int(0, strlen($characters) - 1)];
            }
        } while (Link::where('key', $key)->exists());

        return $key;
    }
}
