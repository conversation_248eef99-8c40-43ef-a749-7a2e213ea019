<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Domain;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class DomainController extends Controller
{
    /**
     * Display a listing of domains for a project.
     */
    public function index(Project $project)
    {
        $domains = Domain::withOptimizedSelect()
            ->forProject($project->id)
            ->unarchived()
            ->with(['project:id,name,slug'])
            ->orderBy('primary', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'data' => $domains,
            'meta' => [
                'total' => $domains->count(),
                'primary_domain' => $domains->where('primary', true)->first(),
            ]
        ]);
    }

    /**
     * Store a newly created domain.
     */
    public function store(Request $request, Project $project)
    {
        $validated = $request->validate([
            'slug' => [
                'required',
                'string',
                'max:255',
                'regex:/^[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*$/',
                Rule::unique('domains', 'slug'),
            ],
            'target' => 'nullable|url|max:500',
            'type' => 'in:redirect,rewrite',
            'description' => 'nullable|string|max:1000',
            'public_stats' => 'boolean',
        ]);

        $domain = Domain::create([
            ...$validated,
            'id' => Str::random(12),
            'project_id' => $project->id,
            'user_id' => auth()->id(),
            'verified' => false, // Domains need to be verified
        ]);

        return response()->json([
            'data' => $domain->load(['project:id,name,slug']),
            'message' => 'Domain created successfully. Please verify domain ownership.',
        ], 201);
    }

    /**
     * Display the specified domain.
     */
    public function show(Project $project, Domain $domain)
    {
        // Ensure domain belongs to project
        if ($domain->project_id !== $project->id) {
            return response()->json(['error' => 'Domain not found'], 404);
        }

        return response()->json([
            'data' => $domain->load(['project:id,name,slug', 'links' => function($query) {
                $query->select(['id', 'domain', 'key', 'url', 'title', 'clicks', 'created_at'])
                      ->orderBy('created_at', 'desc')
                      ->limit(10);
            }])
        ]);
    }

    /**
     * Update the specified domain.
     */
    public function update(Request $request, Project $project, Domain $domain)
    {
        // Ensure domain belongs to project
        if ($domain->project_id !== $project->id) {
            return response()->json(['error' => 'Domain not found'], 404);
        }

        $validated = $request->validate([
            'target' => 'nullable|url|max:500',
            'type' => 'in:redirect,rewrite',
            'description' => 'nullable|string|max:1000',
            'public_stats' => 'boolean',
            'archived' => 'boolean',
        ]);

        $domain->update($validated);

        return response()->json([
            'data' => $domain->fresh(['project:id,name,slug']),
            'message' => 'Domain updated successfully',
        ]);
    }

    /**
     * Set domain as primary.
     */
    public function setPrimary(Project $project, Domain $domain)
    {
        // Ensure domain belongs to project and is verified
        if ($domain->project_id !== $project->id) {
            return response()->json(['error' => 'Domain not found'], 404);
        }

        if (!$domain->verified) {
            return response()->json(['error' => 'Domain must be verified before setting as primary'], 400);
        }

        // Remove primary status from other domains
        Domain::forProject($project->id)->update(['primary' => false]);

        // Set this domain as primary
        $domain->update(['primary' => true]);

        return response()->json([
            'data' => $domain->fresh(['project:id,name,slug']),
            'message' => 'Domain set as primary successfully',
        ]);
    }

    /**
     * Verify domain ownership.
     */
    public function verify(Project $project, Domain $domain)
    {
        // Ensure domain belongs to project
        if ($domain->project_id !== $project->id) {
            return response()->json(['error' => 'Domain not found'], 404);
        }

        // TODO: Implement actual domain verification logic
        // This would typically involve DNS checks or file verification

        $domain->update(['verified' => true]);

        return response()->json([
            'data' => $domain->fresh(['project:id,name,slug']),
            'message' => 'Domain verified successfully',
        ]);
    }

    /**
     * Remove the specified domain.
     */
    public function destroy(Project $project, Domain $domain)
    {
        // Ensure domain belongs to project
        if ($domain->project_id !== $project->id) {
            return response()->json(['error' => 'Domain not found'], 404);
        }

        // Prevent deletion of primary domain
        if ($domain->primary) {
            return response()->json(['error' => 'Cannot delete primary domain'], 400);
        }

        $domain->delete();

        return response()->json([
            'message' => 'Domain deleted successfully',
        ]);
    }
}
