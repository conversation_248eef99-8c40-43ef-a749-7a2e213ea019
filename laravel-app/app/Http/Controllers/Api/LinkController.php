<?php

namespace App\Http\Controllers\Api;

use App\Actions\Links\CreateLinkAction;
use App\Actions\Links\GetLinksAction;
use App\Actions\Links\UpdateLinkAction;
use App\Http\Controllers\Controller;
use App\Models\Link;
use App\Models\Project;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class LinkController extends Controller
{
    public function __construct(
        private CreateLinkAction $createLinkAction,
        private GetLinksAction $getLinksAction,
        private UpdateLinkAction $updateLinkAction
    ) {}

    public function index(Request $request, Project $project): JsonResponse
    {
        $filters = $request->only([
            'archived', 'domain', 'folder_id', 'search',
            'tag', 'sort', 'order', 'per_page'
        ]);

        $links = $this->getLinksAction->execute($project, $filters);

        return response()->json([
            'data' => $links->items(),
            'meta' => [
                'current_page' => $links->currentPage(),
                'total' => $links->total(),
                'per_page' => $links->perPage(),
                'last_page' => $links->lastPage(),
            ]
        ]);
    }

    public function store(Request $request, Project $project): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'url' => 'required|url|max:2048',
            'domain' => 'required|string|max:255',
            'key' => 'nullable|string|max:255',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:280',
            'image' => 'nullable|url',
            'password' => 'nullable|string|min:4',
            'expires_at' => 'nullable|date',
            'track_conversion' => 'boolean',
            'public_stats' => 'boolean',
            'proxy' => 'boolean',
            'rewrite' => 'boolean',
            'do_index' => 'boolean',
            'utm_source' => 'nullable|string|max:255',
            'utm_medium' => 'nullable|string|max:255',
            'utm_campaign' => 'nullable|string|max:255',
            'utm_term' => 'nullable|string|max:255',
            'utm_content' => 'nullable|string|max:255',
            'ios' => 'nullable|url',
            'android' => 'nullable|url',
            'geo' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $link = $this->createLinkAction->execute($project, $validator->validated());

            return response()->json([
                'message' => 'Link created successfully',
                'data' => $link
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to create link',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function show(Project $project, Link $link): JsonResponse
    {
        // Ensure link belongs to project
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        // Load relationships with optimized select
        $link->load([
            'project:id,name,slug',
            'user:id,name,email,image',
            'folder:id,name'
        ]);

        return response()->json(['data' => $link]);
    }

    public function update(Request $request, Project $project, Link $link): JsonResponse
    {
        // Ensure link belongs to project
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'url' => 'sometimes|url|max:2048',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:280',
            'image' => 'nullable|url',
            'password' => 'nullable|string|min:4',
            'expires_at' => 'nullable|date',
            'track_conversion' => 'boolean',
            'public_stats' => 'boolean',
            'proxy' => 'boolean',
            'rewrite' => 'boolean',
            'do_index' => 'boolean',
            'utm_source' => 'nullable|string|max:255',
            'utm_medium' => 'nullable|string|max:255',
            'utm_campaign' => 'nullable|string|max:255',
            'utm_term' => 'nullable|string|max:255',
            'utm_content' => 'nullable|string|max:255',
            'ios' => 'nullable|url',
            'android' => 'nullable|url',
            'geo' => 'nullable|array',
            'comments' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updatedLink = $this->updateLinkAction->execute($link, $validator->validated());

            return response()->json([
                'message' => 'Link updated successfully',
                'data' => $updatedLink
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to update link',
                'message' => $e->getMessage()
            ], 400);
        }
    }

    public function destroy(Project $project, Link $link): JsonResponse
    {
        // Ensure link belongs to project
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        $link->delete();

        return response()->json([
            'message' => 'Link deleted successfully'
        ]);
    }

    public function archive(Project $project, Link $link): JsonResponse
    {
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        $archivedLink = $this->updateLinkAction->archive($link);

        return response()->json([
            'message' => 'Link archived successfully',
            'data' => $archivedLink
        ]);
    }

    public function unarchive(Project $project, Link $link): JsonResponse
    {
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        $unarchivedLink = $this->updateLinkAction->unarchive($link);

        return response()->json([
            'message' => 'Link unarchived successfully',
            'data' => $unarchivedLink
        ]);
    }

    public function stats(Project $project): JsonResponse
    {
        $stats = $this->getLinksAction->getLinkStats($project);

        return response()->json(['data' => $stats]);
    }
}
