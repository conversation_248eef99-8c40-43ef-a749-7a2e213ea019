<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class TagController extends Controller
{
    /**
     * Display a listing of tags for a project.
     */
    public function index(Project $project)
    {
        $tags = Tag::withOptimizedSelect()
            ->forProject($project->id)
            ->withLinksCount()
            ->orderBy('name')
            ->get();

        return response()->json([
            'data' => $tags,
            'meta' => [
                'total' => $tags->count(),
            ]
        ]);
    }

    /**
     * Store a newly created tag.
     */
    public function store(Request $request, Project $project)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:50',
                Rule::unique('tags', 'name')->where('project_id', $project->id),
            ],
            'color' => 'nullable|string|regex:/^#[A-Fa-f0-9]{6}$/',
            'description' => 'nullable|string|max:500',
        ]);

        $tag = Tag::create([
            ...$validated,
            'id' => Str::random(12),
            'project_id' => $project->id,
            'color' => $validated['color'] ?? '#8B5CF6',
        ]);

        return response()->json([
            'data' => $tag,
            'message' => 'Tag created successfully',
        ], 201);
    }

    /**
     * Display the specified tag.
     */
    public function show(Project $project, Tag $tag)
    {
        // Ensure tag belongs to project
        if ($tag->project_id !== $project->id) {
            return response()->json(['error' => 'Tag not found'], 404);
        }

        return response()->json([
            'data' => $tag->load(['links' => function($query) {
                $query->select(['id', 'domain', 'key', 'url', 'title', 'clicks', 'created_at'])
                      ->orderBy('created_at', 'desc');
            }])
        ]);
    }

    /**
     * Update the specified tag.
     */
    public function update(Request $request, Project $project, Tag $tag)
    {
        // Ensure tag belongs to project
        if ($tag->project_id !== $project->id) {
            return response()->json(['error' => 'Tag not found'], 404);
        }

        $validated = $request->validate([
            'name' => [
                'sometimes',
                'string',
                'max:50',
                Rule::unique('tags', 'name')->where('project_id', $project->id)->ignore($tag->id),
            ],
            'color' => 'sometimes|string|regex:/^#[A-Fa-f0-9]{6}$/',
            'description' => 'nullable|string|max:500',
        ]);

        $tag->update($validated);

        return response()->json([
            'data' => $tag->fresh(),
            'message' => 'Tag updated successfully',
        ]);
    }

    /**
     * Remove the specified tag.
     */
    public function destroy(Project $project, Tag $tag)
    {
        // Ensure tag belongs to project
        if ($tag->project_id !== $project->id) {
            return response()->json(['error' => 'Tag not found'], 404);
        }

        // Detach tag from all links before deletion
        $tag->links()->detach();
        $tag->delete();

        return response()->json([
            'message' => 'Tag deleted successfully',
        ]);
    }

    /**
     * Attach tag to links.
     */
    public function attachToLinks(Request $request, Project $project, Tag $tag)
    {
        // Ensure tag belongs to project
        if ($tag->project_id !== $project->id) {
            return response()->json(['error' => 'Tag not found'], 404);
        }

        $validated = $request->validate([
            'link_ids' => 'required|array',
            'link_ids.*' => 'string|exists:links,id',
        ]);

        // Verify all links belong to the project
        $links = $project->links()->whereIn('id', $validated['link_ids'])->get();

        if ($links->count() !== count($validated['link_ids'])) {
            return response()->json(['error' => 'Some links not found in project'], 404);
        }

        // Attach tag to links
        $tag->links()->syncWithoutDetaching($validated['link_ids']);

        return response()->json([
            'data' => $tag->fresh(['links']),
            'message' => 'Tag attached to links successfully',
        ]);
    }

    /**
     * Detach tag from links.
     */
    public function detachFromLinks(Request $request, Project $project, Tag $tag)
    {
        // Ensure tag belongs to project
        if ($tag->project_id !== $project->id) {
            return response()->json(['error' => 'Tag not found'], 404);
        }

        $validated = $request->validate([
            'link_ids' => 'required|array',
            'link_ids.*' => 'string|exists:links,id',
        ]);

        // Detach tag from specified links
        $tag->links()->detach($validated['link_ids']);

        return response()->json([
            'data' => $tag->fresh(['links']),
            'message' => 'Tag detached from links successfully',
        ]);
    }
}
