<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Analytics;
use App\Models\Link;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    /**
     * Get analytics overview for a project.
     */
    public function projectOverview(Request $request, Project $project)
    {
        $validated = $request->validate([
            'period' => 'in:24h,7d,30d,90d,1y,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $period = $validated['period'] ?? '30d';
        $query = Analytics::forProject($project->id);

        // Apply date filters
        $query = $this->applyDateFilter($query, $period, $validated);

        // Get basic stats
        $totalClicks = $query->clone()->byEvent('click')->count();
        $totalConversions = $query->clone()->byEvent('conversion')->count();
        $totalLeads = $query->clone()->byEvent('lead')->count();
        $totalSales = $query->clone()->byEvent('sale')->count();

        // Get top countries
        $topCountries = $query->clone()
            ->byEvent('click')
            ->groupByCountry()
            ->limit(10)
            ->get();

        // Get top devices
        $topDevices = $query->clone()
            ->byEvent('click')
            ->groupByDevice()
            ->limit(10)
            ->get();

        // Get top browsers
        $topBrowsers = $query->clone()
            ->byEvent('click')
            ->groupByBrowser()
            ->limit(10)
            ->get();

        // Get top referers
        $topReferers = $query->clone()
            ->byEvent('click')
            ->groupByReferer()
            ->limit(10)
            ->get();

        // Get clicks over time
        $clicksOverTime = $query->clone()
            ->byEvent('click')
            ->groupByDate()
            ->get();

        return response()->json([
            'data' => [
                'overview' => [
                    'total_clicks' => $totalClicks,
                    'total_conversions' => $totalConversions,
                    'total_leads' => $totalLeads,
                    'total_sales' => $totalSales,
                    'conversion_rate' => $totalClicks > 0 ? round(($totalConversions / $totalClicks) * 100, 2) : 0,
                ],
                'top_countries' => $topCountries,
                'top_devices' => $topDevices,
                'top_browsers' => $topBrowsers,
                'top_referers' => $topReferers,
                'clicks_over_time' => $clicksOverTime,
            ],
            'meta' => [
                'period' => $period,
                'start_date' => $validated['start_date'] ?? null,
                'end_date' => $validated['end_date'] ?? null,
            ]
        ]);
    }

    /**
     * Get analytics for a specific link.
     */
    public function linkAnalytics(Request $request, Project $project, Link $link)
    {
        // Ensure link belongs to project
        if ($link->project_id !== $project->id) {
            return response()->json(['error' => 'Link not found'], 404);
        }

        $validated = $request->validate([
            'period' => 'in:24h,7d,30d,90d,1y,all',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $period = $validated['period'] ?? '30d';
        $query = Analytics::forLink($link->id);

        // Apply date filters
        $query = $this->applyDateFilter($query, $period, $validated);

        // Get basic stats
        $totalClicks = $query->clone()->byEvent('click')->count();
        $totalConversions = $query->clone()->byEvent('conversion')->count();
        $uniqueClicks = $query->clone()->byEvent('click')->distinct('ip')->count();

        // Get geographic breakdown
        $countries = $query->clone()
            ->byEvent('click')
            ->groupByCountry()
            ->get();

        // Get device breakdown
        $devices = $query->clone()
            ->byEvent('click')
            ->groupByDevice()
            ->get();

        // Get referer breakdown
        $referers = $query->clone()
            ->byEvent('click')
            ->groupByReferer()
            ->get();

        // Get clicks over time
        $clicksOverTime = $query->clone()
            ->byEvent('click')
            ->groupByDate()
            ->get();

        return response()->json([
            'data' => [
                'link' => $link->only(['id', 'domain', 'key', 'url', 'title']),
                'overview' => [
                    'total_clicks' => $totalClicks,
                    'unique_clicks' => $uniqueClicks,
                    'total_conversions' => $totalConversions,
                    'conversion_rate' => $totalClicks > 0 ? round(($totalConversions / $totalClicks) * 100, 2) : 0,
                ],
                'countries' => $countries,
                'devices' => $devices,
                'referers' => $referers,
                'clicks_over_time' => $clicksOverTime,
            ],
            'meta' => [
                'period' => $period,
                'start_date' => $validated['start_date'] ?? null,
                'end_date' => $validated['end_date'] ?? null,
            ]
        ]);
    }

    /**
     * Track an event (click, conversion, etc.).
     */
    public function track(Request $request)
    {
        $validated = $request->validate([
            'link_id' => 'required|string|exists:links,id',
            'event' => 'required|in:click,conversion,lead,sale',
            'metadata' => 'nullable|array',
            'country' => 'nullable|string|max:2',
            'city' => 'nullable|string|max:100',
            'region' => 'nullable|string|max:100',
            'device' => 'nullable|string|max:50',
            'browser' => 'nullable|string|max:50',
            'os' => 'nullable|string|max:50',
            'referer' => 'nullable|string|max:255',
            'ip' => 'nullable|ip',
        ]);

        $link = Link::findOrFail($validated['link_id']);

        // Create analytics record
        $analytics = Analytics::create([
            'link_id' => $link->id,
            'project_id' => $link->project_id,
            'user_id' => $link->user_id,
            'event' => $validated['event'],
            'metadata' => $validated['metadata'] ?? null,
            'country' => $validated['country'] ?? null,
            'city' => $validated['city'] ?? null,
            'region' => $validated['region'] ?? null,
            'device' => $validated['device'] ?? null,
            'browser' => $validated['browser'] ?? null,
            'os' => $validated['os'] ?? null,
            'referer' => $validated['referer'] ?? null,
            'ip' => $validated['ip'] ?? $request->ip(),
            'ua' => $request->userAgent(),
            'timestamp' => now(),
        ]);

        // Update link counters if it's a click event
        if ($validated['event'] === 'click') {
            $link->increment('clicks');
            $link->update(['last_clicked' => now()]);
        }

        return response()->json([
            'data' => $analytics,
            'message' => 'Event tracked successfully',
        ], 201);
    }

    /**
     * Apply date filter to query based on period.
     */
    private function applyDateFilter($query, string $period, array $validated)
    {
        if (!empty($validated['start_date']) && !empty($validated['end_date'])) {
            return $query->inDateRange($validated['start_date'], $validated['end_date']);
        }

        return match($period) {
            '24h' => $query->where('timestamp', '>=', now()->subDay()),
            '7d' => $query->lastDays(7),
            '30d' => $query->lastDays(30),
            '90d' => $query->lastDays(90),
            '1y' => $query->lastDays(365),
            'all' => $query,
            default => $query->lastDays(30),
        };
    }
}
