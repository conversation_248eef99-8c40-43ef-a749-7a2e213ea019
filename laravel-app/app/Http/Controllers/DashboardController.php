<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function index(Request $request): Response
    {
        // Get the authenticated user
        /** @var User $user */
        $user = $request->user();

        $projects = Project::where('user_id', $user->id)
            ->select(['id', 'name', 'slug', 'description', 'plan', 'created_at'])
            ->withCount(['links', 'domains'])
            ->with(['analytics' => function ($query) {
                $query->selectRaw('project_id, SUM(clicks) as total_clicks')
                    ->groupBy('project_id');
            }])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($project) {
                return [
                    'id' => $project->id,
                    'name' => $project->name,
                    'slug' => $project->slug,
                    'description' => $project->description,
                    'plan' => $project->plan,
                    'total_links' => $project->links_count,
                    'total_clicks' => $project->analytics->sum('total_clicks') ?? 0,
                    'created_at' => $project->created_at->toISOString(),
                ];
            });

        $stats = [
            'total_projects' => $projects->count(),
            'total_links' => $projects->sum('total_links'),
            'total_clicks' => $projects->sum('total_clicks'),
            'click_growth' => 12.5, // Sample growth percentage
        ];

                return Inertia::render('Dashboard', [
            'projects' => $projects,
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'image' => $user->image ?? 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face'
            ],
            'stats' => $stats,
        ]);
    }

    public function indexWithAuth(Request $request): Response
    {
        // This is how it would work with actual authentication
        /** @var User $user */
        $user = $request->user();

        // Get user's projects with optimized select and aggregated stats
        $projects = Project::whereHas('users', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->select([
                'id', 'name', 'slug', 'plan',
                'total_links', 'total_clicks'
            ])
            ->get()
            ->toArray();

        return Inertia::render('Dashboard', [
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'image' => $user->image,
            ],
            'projects' => $projects,
        ]);
    }
}
