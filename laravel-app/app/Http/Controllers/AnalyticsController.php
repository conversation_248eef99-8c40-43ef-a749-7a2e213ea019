<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\Analytics;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\DB;

class AnalyticsController extends Controller
{
    public function index(Request $request, Project $project)
    {
        // Get analytics data for the project
        $totalClicks = $project->analytics()->where('event', 'click')->count();
        $totalLinks = $project->links()->count();

        // Calculate growth (for demo, using last 30 vs previous 30 days)
        $last30Days = $project->analytics()
            ->where('event', 'click')
            ->where('timestamp', '>=', now()->subDays(30))
            ->count();

        $previous30Days = $project->analytics()
            ->where('event', 'click')
            ->where('timestamp', '>=', now()->subDays(60))
            ->where('timestamp', '<', now()->subDays(30))
            ->count();

        $clickGrowth = $previous30Days > 0 ?
            round((($last30Days - $previous30Days) / $previous30Days) * 100, 1) :
            0;

        // Top countries
        $topCountries = $project->analytics()
            ->where('event', 'click')
            ->select('country', DB::raw('COUNT(*) as clicks'))
            ->groupBy('country')
            ->orderByDesc('clicks')
            ->limit(5)
            ->get()
            ->map(function ($item) use ($totalClicks) {
                return [
                    'country' => $item->country,
                    'clicks' => $item->clicks,
                    'percentage' => $totalClicks > 0 ? round(($item->clicks / $totalClicks) * 100, 1) : 0,
                ];
            });

        // Top devices
        $topDevices = $project->analytics()
            ->where('event', 'click')
            ->select('device', DB::raw('COUNT(*) as clicks'))
            ->groupBy('device')
            ->orderByDesc('clicks')
            ->limit(5)
            ->get()
            ->map(function ($item) use ($totalClicks) {
                return [
                    'device' => $item->device,
                    'clicks' => $item->clicks,
                    'percentage' => $totalClicks > 0 ? round(($item->clicks / $totalClicks) * 100, 1) : 0,
                ];
            });

        // Click history (last 30 days)
        $clickHistory = $project->analytics()
            ->where('event', 'click')
            ->where('timestamp', '>=', now()->subDays(30))
            ->select(
                DB::raw('DATE(timestamp) as date'),
                DB::raw('COUNT(*) as clicks')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        $analytics = [
            'total_clicks' => $totalClicks,
            'total_links' => $totalLinks,
            'click_growth' => $clickGrowth,
            'top_countries' => $topCountries,
            'top_devices' => $topDevices,
            'click_history' => $clickHistory,
        ];

        return Inertia::render('Analytics/Index', [
            'analytics' => $analytics,
            'project' => $project->only(['id', 'name', 'slug']),
        ]);
    }
}
