<?php

namespace App\Http\Controllers;

use App\Models\Analytics;
use App\Models\Link;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class RedirectController extends Controller
{
    public function redirect(Request $request, string $domain, string $key)
    {
        // Find the link by domain and key
        $link = Link::whereHas('project.domains', function ($query) use ($domain) {
            $query->where('slug', $domain);
        })
        ->where('key', $key)
        ->where('archived', false)
        ->first();

        if (!$link) {
            abort(404, 'Link not found');
        }

        // Check if link has expired
        if ($link->expires_at && $link->expires_at->isPast()) {
            abort(410, 'Link has expired');
        }

        // Track the click
        $this->trackClick($request, $link);

        // Increment click count
        $link->increment('clicks');
        $link->update(['last_clicked' => now()]);

        // Redirect to the target URL
        return redirect($link->url, $link->rewrite ? 200 : 301);
    }

    private function trackClick(Request $request, Link $link): void
    {
        try {
            Analytics::create([
                'id' => Str::random(12),
                'link_id' => $link->id,
                'project_id' => $link->project_id,
                'user_id' => $link->user_id,
                'event' => 'click',
                'country' => $this->getCountryFromIP($request->ip()),
                'device' => $this->getDeviceType($request->userAgent()),
                'browser' => $this->getBrowser($request->userAgent()),
                'referer' => $this->getReferer($request->header('referer')),
                'ip' => $request->ip(),
                'ua' => $request->userAgent(),
                'timestamp' => now(),
            ]);
        } catch (\Exception $e) {
            // Log error but don't fail the redirect
            logger()->error('Failed to track click', [
                'link_id' => $link->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    private function getCountryFromIP(string $ip): string
    {
        // In a real implementation, you would use a GeoIP service
        // For demo purposes, return a default country
        return 'US';
    }

    private function getDeviceType(?string $userAgent): string
    {
        if (!$userAgent) {
            return 'Unknown';
        }

        $userAgent = strtolower($userAgent);

        if (str_contains($userAgent, 'mobile') || str_contains($userAgent, 'android')) {
            return 'Mobile';
        }

        if (str_contains($userAgent, 'tablet') || str_contains($userAgent, 'ipad')) {
            return 'Tablet';
        }

        return 'Desktop';
    }

    private function getBrowser(?string $userAgent): string
    {
        if (!$userAgent) {
            return 'Unknown';
        }

        $userAgent = strtolower($userAgent);

        if (str_contains($userAgent, 'edge')) {
            return 'Edge';
        }

        if (str_contains($userAgent, 'chrome')) {
            return 'Chrome';
        }

        if (str_contains($userAgent, 'firefox')) {
            return 'Firefox';
        }

        if (str_contains($userAgent, 'safari')) {
            return 'Safari';
        }

        return 'Unknown';
    }

    private function getReferer(?string $referer): string
    {
        if (!$referer) {
            return 'direct';
        }

        $domain = parse_url($referer, PHP_URL_HOST);

        if (!$domain) {
            return 'direct';
        }

        // Extract main domain (remove www, etc.)
        $domain = preg_replace('/^www\./', '', $domain);

        return $domain;
    }
}
