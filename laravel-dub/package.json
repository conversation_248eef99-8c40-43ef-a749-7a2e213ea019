{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@headlessui/react": "^2.2.4", "@inertiajs/react": "^2.0.0", "@tailwindcss/forms": "^0.5.6", "@tailwindcss/vite": "^4.0.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.12", "axios": "^1.8.2", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.2.0", "postcss": "^8.4.31", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwindcss": "^3.2.1", "vite": "^6.2.4"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/typography": "^0.5.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.462.0", "tailwind-merge": "^2.4.0"}}