import { ReactNode } from 'react';
import { Head } from '@inertiajs/react';
import { User } from '@/types';
import Sidebar from '@/Components/Dashboard/Sidebar';
import Header from '@/Components/Dashboard/Header';

interface DashboardLayoutProps {
  children: ReactNode;
  title?: string;
  user: User;
  workspace?: any;
}

export default function DashboardLayout({ 
  children, 
  title = 'Dashboard',
  user,
  workspace 
}: DashboardLayoutProps) {
  return (
    <>
      <Head title={title} />
      <div className="min-h-screen bg-gray-50">
        <div className="flex h-screen">
          {/* Sidebar */}
          <Sidebar user={user} workspace={workspace} />
          
          {/* Main content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <Header user={user} workspace={workspace} />
            
            {/* Page content */}
            <main className="flex-1 overflow-y-auto">
              <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                  {children}
                </div>
              </div>
            </main>
          </div>
        </div>
      </div>
    </>
  );
}
