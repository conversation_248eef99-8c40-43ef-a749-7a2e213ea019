export interface User {
  id: string;
  name?: string;
  email?: string;
  image?: string;
  is_machine: boolean;
  subscribed: boolean;
  source?: string;
  default_workspace?: string;
  default_partner_id?: string;
  email_verified_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Workspace {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  invite_code?: string;
  default_program_id?: string;
  plan: string;
  stripe_id?: string;
  billing_cycle_start: number;
  payment_failed_at?: string;
  invoice_prefix?: string;
  stripe_connect_id?: string;
  shopify_store_id?: string;
  total_links: number;
  total_clicks: number;
  usage: number;
  usage_limit: number;
  links_usage: number;
  links_limit: number;
  sales_usage: number;
  sales_limit: number;
  domains_limit: number;
  tags_limit: number;
  folders_usage: number;
  folders_limit: number;
  users_limit: number;
  ai_usage: number;
  ai_limit: number;
  referral_link_id?: string;
  referred_signups: number;
  store?: any;
  allowed_hostnames?: string[];
  conversion_enabled: boolean;
  webhook_enabled: boolean;
  partners_enabled: boolean;
  sso_enabled: boolean;
  dot_link_claimed: boolean;
  usage_last_checked: string;
  created_at: string;
  updated_at: string;
}

export interface Link {
  id: string;
  domain: string;
  key: string;
  url: string;
  short_link: string;
  archived: boolean;
  expires_at?: string;
  expired_url?: string;
  password?: string;
  track_conversion: boolean;
  proxy: boolean;
  title?: string;
  description?: string;
  image?: string;
  video?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  rewrite: boolean;
  do_index: boolean;
  ios?: string;
  android?: string;
  geo?: any;
  test_variants?: any;
  test_started_at?: string;
  test_completed_at?: string;
  user_id?: string;
  workspace_id?: string;
  program_id?: string;
  folder_id?: string;
  external_id?: string;
  tenant_id?: string;
  public_stats: boolean;
  clicks: number;
  last_clicked?: string;
  leads: number;
  sales: number;
  sale_amount: number;
  comments?: string;
  partner_id?: string;
  created_at: string;
  updated_at: string;
}

export interface Domain {
  id: string;
  slug: string;
  verified: boolean;
  placeholder?: string;
  expired_url?: string;
  not_found_url?: string;
  primary: boolean;
  archived: boolean;
  last_checked: string;
  logo?: string;
  apple_app_site_association?: any;
  asset_links?: any;
  workspace_id?: string;
  created_at: string;
  updated_at: string;
}

export interface PageProps<T extends Record<string, unknown> = Record<string, unknown>> {
  auth: {
    user: User;
  };
  workspace?: Workspace;
  [key: string]: any;
}
