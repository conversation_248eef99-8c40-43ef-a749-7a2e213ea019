import { But<PERSON> } from "@/Components/ui/Button";
import DashboardLayout from "@/Layouts/DashboardLayout";
import { PageProps, Workspace } from "@/types";
import {
  CalendarIcon,
  ChartBarIcon,
  CursorArrowRaysIcon,
  LinkIcon,
  PlusIcon,
} from "@heroicons/react/24/outline";
import { <PERSON>, <PERSON> } from "@inertiajs/react";

interface DashboardProps extends PageProps {
  workspace?: Workspace;
  recentLinks: LinkType[];
  analytics: {
    total_links: number;
    total_clicks: number;
    links_this_month: number;
    clicks_this_month: number;
  };
}

export default function Dashboard({
  auth,
  workspace,
  recentLinks,
  analytics,
}: DashboardProps) {
  const stats = [
    {
      name: "Total Links",
      value: analytics.total_links.toLocaleString(),
      icon: LinkIcon,
      change: `+${analytics.links_this_month}`,
      changeType: "increase",
    },
    {
      name: "Total Clicks",
      value: analytics.total_clicks.toLocaleString(),
      icon: CursorArrowRaysIcon,
      change: `+${analytics.clicks_this_month}`,
      changeType: "increase",
    },
    {
      name: "Links This Month",
      value: analytics.links_this_month.toLocaleString(),
      icon: CalendarIcon,
      change: "this month",
      changeType: "neutral",
    },
    {
      name: "Clicks This Month",
      value: analytics.clicks_this_month.toLocaleString(),
      icon: ChartBarIcon,
      change: "this month",
      changeType: "neutral",
    },
  ];

  return (
    <DashboardLayout user={auth.user} workspace={workspace} title="Dashboard">
      <Head title="Dashboard" />

      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back{workspace ? ` to ${workspace.name}` : ""}!
            </h1>
            <p className="mt-1 text-sm text-gray-500">
              Here's what's happening with your links today.
            </p>
          </div>
          <Link href="/dashboard/links/create">
            <Button
              text="Create Link"
              icon={<PlusIcon className="h-4 w-4" />}
            />
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <div
              key={stat.name}
              className="relative overflow-hidden rounded-lg bg-white px-4 pb-12 pt-5 shadow sm:px-6 sm:pt-6"
            >
              <dt>
                <div className="absolute rounded-md bg-indigo-500 p-3">
                  <stat.icon
                    className="h-6 w-6 text-white"
                    aria-hidden="true"
                  />
                </div>
                <p className="ml-16 truncate text-sm font-medium text-gray-500">
                  {stat.name}
                </p>
              </dt>
              <dd className="ml-16 flex items-baseline pb-6 sm:pb-7">
                <p className="text-2xl font-semibold text-gray-900">
                  {stat.value}
                </p>
                <p className="ml-2 flex items-baseline text-sm font-semibold text-gray-500">
                  {stat.change}
                </p>
              </dd>
            </div>
          ))}
        </div>

        {/* Recent Links */}
        <div className="rounded-lg bg-white shadow">
          <div className="px-4 py-5 sm:p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                Recent Links
              </h3>
              <Link href="/dashboard/links">
                <Button
                  text="View All"
                  variant="outline"
                  className="h-8 text-xs"
                />
              </Link>
            </div>

            {recentLinks.length > 0 ? (
              <div className="space-y-3">
                {recentLinks.map((link) => (
                  <div
                    key={link.id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 p-3 hover:bg-gray-50"
                  >
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <LinkIcon className="h-5 w-5 text-gray-400" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="truncate text-sm font-medium text-gray-900">
                            {link.short_link}
                          </p>
                          <p className="truncate text-sm text-gray-500">
                            {link.url}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>{link.clicks} clicks</span>
                      <span>
                        {new Date(link.created_at).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-6 text-center">
                <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">
                  No links yet
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Get started by creating your first short link.
                </p>
                <div className="mt-6">
                  <Link href="/dashboard/links/create">
                    <Button
                      text="Create Link"
                      icon={<PlusIcon className="h-4 w-4" />}
                    />
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
