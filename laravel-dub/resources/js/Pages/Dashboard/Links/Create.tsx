import { Head, useForm, <PERSON> } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps, Domain, Workspace } from '@/types';
import { Button } from '@/Components/ui/Button';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { FormEventHandler } from 'react';

interface CreateLinkProps extends PageProps {
  workspace?: Workspace;
  domains: Domain[];
}

export default function CreateLink({ 
  auth, 
  workspace, 
  domains 
}: CreateLinkProps) {
  const { data, setData, post, processing, errors, reset } = useForm({
    url: '',
    key: '',
    domain: domains.length > 0 ? domains[0].slug : '',
    title: '',
    description: '',
  });

  const submit: FormEventHandler = (e) => {
    e.preventDefault();
    post(route('links.store'));
  };

  return (
    <DashboardLayout user={auth.user} workspace={workspace} title="Create Link">
      <Head title="Create Link" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/links">
            <Button
              variant="outline"
              icon={<ArrowLeftIcon className="h-4 w-4" />}
              className="h-9"
            />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Create Link</h1>
            <p className="mt-1 text-sm text-gray-500">
              Create a new short link to share and track.
            </p>
          </div>
        </div>

        {/* Form */}
        <div className="bg-white shadow rounded-lg">
          <form onSubmit={submit} className="space-y-6 p-6">
            {/* Destination URL */}
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                Destination URL *
              </label>
              <div className="mt-1">
                <input
                  id="url"
                  name="url"
                  type="url"
                  value={data.url}
                  onChange={(e) => setData('url', e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="https://example.com"
                  required
                />
              </div>
              {errors.url && (
                <p className="mt-2 text-sm text-red-600">{errors.url}</p>
              )}
            </div>

            {/* Short Link Configuration */}
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {/* Domain */}
              <div>
                <label htmlFor="domain" className="block text-sm font-medium text-gray-700">
                  Domain *
                </label>
                <div className="mt-1">
                  <select
                    id="domain"
                    name="domain"
                    value={data.domain}
                    onChange={(e) => setData('domain', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    required
                  >
                    {domains.map((domain) => (
                      <option key={domain.id} value={domain.slug}>
                        {domain.slug}
                      </option>
                    ))}
                  </select>
                </div>
                {errors.domain && (
                  <p className="mt-2 text-sm text-red-600">{errors.domain}</p>
                )}
              </div>

              {/* Custom Key */}
              <div>
                <label htmlFor="key" className="block text-sm font-medium text-gray-700">
                  Custom Key (optional)
                </label>
                <div className="mt-1">
                  <input
                    id="key"
                    name="key"
                    type="text"
                    value={data.key}
                    onChange={(e) => setData('key', e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="my-link"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  Leave empty to generate automatically
                </p>
                {errors.key && (
                  <p className="mt-2 text-sm text-red-600">{errors.key}</p>
                )}
              </div>
            </div>

            {/* Preview */}
            {data.domain && (
              <div className="bg-gray-50 rounded-lg p-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Short Link Preview
                </label>
                <div className="text-sm text-blue-600 font-mono">
                  https://{data.domain}/{data.key || '[auto-generated]'}
                </div>
              </div>
            )}

            {/* Metadata */}
            <div className="space-y-6">
              <h3 className="text-lg font-medium text-gray-900">
                Metadata (Optional)
              </h3>
              
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                  Title
                </label>
                <div className="mt-1">
                  <input
                    id="title"
                    name="title"
                    type="text"
                    value={data.title}
                    onChange={(e) => setData('title', e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Link title for social sharing"
                  />
                </div>
                {errors.title && (
                  <p className="mt-2 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <div className="mt-1">
                  <textarea
                    id="description"
                    name="description"
                    rows={3}
                    value={data.description}
                    onChange={(e) => setData('description', e.target.value)}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Link description for social sharing"
                  />
                </div>
                <p className="mt-2 text-sm text-gray-500">
                  {data.description.length}/280 characters
                </p>
                {errors.description && (
                  <p className="mt-2 text-sm text-red-600">{errors.description}</p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
              <Link href="/dashboard/links">
                <Button
                  variant="outline"
                  text="Cancel"
                  disabled={processing}
                />
              </Link>
              <Button
                text="Create Link"
                loading={processing}
                disabled={processing || !data.url || !data.domain}
              />
            </div>
          </form>
        </div>
      </div>
    </DashboardLayout>
  );
}
