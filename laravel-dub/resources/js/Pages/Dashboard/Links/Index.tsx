import { Head, Link, router } from '@inertiajs/react';
import DashboardLayout from '@/Layouts/DashboardLayout';
import { PageProps, Link as LinkType, Workspace } from '@/types';
import { Button } from '@/Components/ui/Button';
import { 
  PlusIcon,
  LinkIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ClipboardDocumentIcon
} from '@heroicons/react/24/outline';
import { useState } from 'react';

interface LinksIndexProps extends PageProps {
  workspace?: Workspace;
  links: {
    data: LinkType[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export default function LinksIndex({ 
  auth, 
  workspace, 
  links 
}: LinksIndexProps) {
  const [copiedLink, setCopiedLink] = useState<string | null>(null);

  const copyToClipboard = async (shortLink: string) => {
    try {
      await navigator.clipboard.writeText(shortLink);
      setCopiedLink(shortLink);
      setTimeout(() => setCopiedLink(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const deleteLink = (linkId: string) => {
    if (confirm('Are you sure you want to delete this link?')) {
      router.delete(`/dashboard/links/${linkId}`);
    }
  };

  return (
    <DashboardLayout user={auth.user} workspace={workspace} title="Links">
      <Head title="Links" />
      
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Links</h1>
            <p className="mt-1 text-sm text-gray-500">
              Manage your short links and track their performance.
            </p>
          </div>
          <Link href="/dashboard/links/create">
            <Button
              text="Create Link"
              icon={<PlusIcon className="h-4 w-4" />}
            />
          </Link>
        </div>

        {/* Links Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          {links.data.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Link
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Destination
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Clicks
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {links.data.map((link) => (
                      <tr key={link.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <LinkIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm font-medium text-blue-600">
                                  {link.short_link}
                                </span>
                                <button
                                  onClick={() => copyToClipboard(link.short_link)}
                                  className="text-gray-400 hover:text-gray-600"
                                  title="Copy to clipboard"
                                >
                                  <ClipboardDocumentIcon className="h-4 w-4" />
                                </button>
                                {copiedLink === link.short_link && (
                                  <span className="text-xs text-green-600">Copied!</span>
                                )}
                              </div>
                              {link.title && (
                                <div className="text-sm text-gray-500">{link.title}</div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 truncate max-w-xs">
                            {link.url}
                          </div>
                          {link.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {link.description}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {link.clicks.toLocaleString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(link.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <Link href={`/dashboard/links/${link.id}`}>
                              <button
                                className="text-indigo-600 hover:text-indigo-900"
                                title="View details"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                            </Link>
                            <Link href={`/dashboard/links/${link.id}/edit`}>
                              <button
                                className="text-gray-600 hover:text-gray-900"
                                title="Edit link"
                              >
                                <PencilIcon className="h-4 w-4" />
                              </button>
                            </Link>
                            <button
                              onClick={() => deleteLink(link.id)}
                              className="text-red-600 hover:text-red-900"
                              title="Delete link"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {links.last_page > 1 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    {links.current_page > 1 && (
                      <Link
                        href={`/dashboard/links?page=${links.current_page - 1}`}
                        className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Previous
                      </Link>
                    )}
                    {links.current_page < links.last_page && (
                      <Link
                        href={`/dashboard/links?page=${links.current_page + 1}`}
                        className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                      >
                        Next
                      </Link>
                    )}
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        Showing{' '}
                        <span className="font-medium">
                          {(links.current_page - 1) * links.per_page + 1}
                        </span>{' '}
                        to{' '}
                        <span className="font-medium">
                          {Math.min(links.current_page * links.per_page, links.total)}
                        </span>{' '}
                        of{' '}
                        <span className="font-medium">{links.total}</span> results
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="text-center py-12">
              <LinkIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">
                No links yet
              </h3>
              <p className="mt-1 text-sm text-gray-500">
                Get started by creating your first short link.
              </p>
              <div className="mt-6">
                <Link href="/dashboard/links/create">
                  <Button
                    text="Create Link"
                    icon={<PlusIcon className="h-4 w-4" />}
                  />
                </Link>
              </div>
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
