import { cn } from "@/lib/utils";
import { User, Workspace } from "@/types";
import {
  ChartBarIcon,
  CogIcon,
  GlobeAltIcon,
  HomeIcon,
  LinkIcon,
  UsersIcon,
} from "@heroicons/react/24/outline";
import { Link } from "@inertiajs/react";

interface SidebarProps {
  user: User;
  workspace?: Workspace;
}

const navigation = [
  { name: "Overview", href: "/dashboard", icon: HomeIcon },
  { name: "Links", href: "/dashboard/links", icon: LinkIcon },
  { name: "Analytics", href: "/dashboard/analytics", icon: ChartBarIcon },
  { name: "Domains", href: "/dashboard/domains", icon: GlobeAltIcon },
  { name: "Team", href: "/dashboard/team", icon: UsersIcon },
  { name: "Settings", href: "/dashboard/settings", icon: CogIcon },
];

export default function Sidebar({ user, workspace }: SidebarProps) {
  const { url } = usePage();

  return (
    <div className="hidden md:flex md:w-64 md:flex-col">
      <div className="flex flex-grow flex-col overflow-y-auto border-r border-gray-200 bg-white pt-5">
        <div className="flex flex-shrink-0 items-center px-4">
          <div className="flex items-center">
            {workspace?.logo ? (
              <img
                className="h-8 w-8 rounded-md"
                src={workspace.logo}
                alt={workspace.name}
              />
            ) : (
              <div className="flex h-8 w-8 items-center justify-center rounded-md bg-black">
                <span className="text-sm font-bold text-white">
                  {workspace?.name?.charAt(0) || "D"}
                </span>
              </div>
            )}
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-900">
                {workspace?.name || "Dub"}
              </p>
              <p className="text-xs text-gray-500">
                {workspace?.plan || "Free"}
              </p>
            </div>
          </div>
        </div>

        <div className="mt-5 flex flex-grow flex-col">
          <nav className="flex-1 space-y-1 px-2">
            {navigation.map((item) => {
              const isActive = url.startsWith(item.href);
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    isActive
                      ? "bg-gray-100 text-gray-900"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                    "group flex items-center rounded-md px-2 py-2 text-sm font-medium",
                  )}
                >
                  <item.icon
                    className={cn(
                      isActive
                        ? "text-gray-500"
                        : "text-gray-400 group-hover:text-gray-500",
                      "mr-3 h-5 w-5 flex-shrink-0",
                    )}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>

        <div className="flex flex-shrink-0 border-t border-gray-200 p-4">
          <div className="flex items-center">
            <div>
              {user.image ? (
                <img
                  className="inline-block h-8 w-8 rounded-full"
                  src={user.image}
                  alt={user.name || "User"}
                />
              ) : (
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-500">
                  <span className="text-sm font-medium text-white">
                    {user.name?.charAt(0) || user.email?.charAt(0) || "U"}
                  </span>
                </div>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-700">
                {user.name || user.email}
              </p>
              <p className="text-xs text-gray-500">{user.email}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
