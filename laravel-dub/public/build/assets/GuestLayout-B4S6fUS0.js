import{j as e,Y as r}from"./app-DTtOcJL_.js";import{A as t}from"./ApplicationLogo-BV1aq7qu.js";function a({children:s}){return e.jsxs("div",{className:"flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0",children:[e.jsx("div",{children:e.jsx(r,{href:"/",children:e.jsx(t,{className:"h-20 w-20 fill-current text-gray-500"})})}),e.jsx("div",{className:"mt-6 w-full overflow-hidden bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg",children:s})]})}export{a as G};
