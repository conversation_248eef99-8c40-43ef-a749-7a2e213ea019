import{j as s,$ as m}from"./app-DTtOcJL_.js";import{A as r}from"./AuthenticatedLayout-CX9KCCFW.js";import t from"./DeleteUserForm-DvngXjlD.js";import i from"./UpdatePasswordForm-DoMXf1_k.js";import o from"./UpdateProfileInformationForm-DW2h92yO.js";import"./ApplicationLogo-BV1aq7qu.js";import"./transition-DUFXa53I.js";import"./TextInput-Du0Ne2w8.js";import"./InputLabel-BAYDz-DW.js";import"./PrimaryButton-CAVkHQZE.js";function g({mustVerifyEmail:e,status:a}){return s.jsxs(r,{header:s.jsx("h2",{className:"text-xl font-semibold leading-tight text-gray-800",children:"Profile"}),children:[s.jsx(m,{title:"Profile"}),s.jsx("div",{className:"py-12",children:s.jsxs("div",{className:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8",children:[s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(o,{mustVerifyEmail:e,status:a,className:"max-w-xl"})}),s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(i,{className:"max-w-xl"})}),s.jsx("div",{className:"bg-white p-4 shadow sm:rounded-lg sm:p-8",children:s.jsx(t,{className:"max-w-xl"})})]})})]})}export{g as default};
