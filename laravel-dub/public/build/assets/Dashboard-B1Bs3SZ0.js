import{j as e,$ as s}from"./app-DTtOcJL_.js";import{A as a}from"./AuthenticatedLayout-CX9KCCFW.js";import"./ApplicationLogo-BV1aq7qu.js";import"./transition-DUFXa53I.js";function o(){return e.jsxs(a,{header:e.jsx("h2",{className:"text-xl font-semibold leading-tight text-gray-800",children:"Dashboard"}),children:[e.jsx(s,{title:"Dashboard"}),e.jsx("div",{className:"py-12",children:e.jsx("div",{className:"mx-auto max-w-7xl sm:px-6 lg:px-8",children:e.jsx("div",{className:"overflow-hidden bg-white shadow-sm sm:rounded-lg",children:e.jsx("div",{className:"p-6 text-gray-900",children:"You're logged in!"})})})})]})}export{o as default};
