{"name": "tightenco/ziggy", "description": "Use your Laravel named routes in JavaScript.", "keywords": ["laravel", "routes", "javascript", "ziggy"], "homepage": "https://github.com/tighten/ziggy", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>-<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=8.1", "ext-json": "*", "laravel/framework": ">=9.0"}, "require-dev": {"laravel/folio": "^1.1", "orchestra/testbench": "^7.0 || ^8.0 || ^9.0 || ^10.0", "pestphp/pest": "^2.26|^3.0", "pestphp/pest-plugin-laravel": "^2.4|^3.0"}, "autoload": {"psr-4": {"Tighten\\Ziggy\\": "src/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "extra": {"laravel": {"providers": ["Tighten\\Ziggy\\ZiggyServiceProvider"]}}, "config": {"optimize-autoloader": true, "sort-packages": true, "allow-plugins": {"kylekatarnls/update-helper": true, "pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}