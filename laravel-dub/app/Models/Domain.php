<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Domain extends Model
{
    use HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'slug',
        'verified',
        'placeholder',
        'expired_url',
        'not_found_url',
        'primary',
        'archived',
        'last_checked',
        'logo',
        'apple_app_site_association',
        'asset_links',
        'workspace_id',
    ];

    protected $casts = [
        'verified' => 'boolean',
        'primary' => 'boolean',
        'archived' => 'boolean',
        'last_checked' => 'datetime',
        'apple_app_site_association' => 'array',
        'asset_links' => 'array',
    ];

    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }
}
