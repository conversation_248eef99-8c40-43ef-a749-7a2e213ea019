<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Workspace extends Model
{
    use HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'name',
        'slug',
        'logo',
        'invite_code',
        'default_program_id',
        'plan',
        'stripe_id',
        'billing_cycle_start',
        'payment_failed_at',
        'invoice_prefix',
        'stripe_connect_id',
        'shopify_store_id',
        'total_links',
        'total_clicks',
        'usage',
        'usage_limit',
        'links_usage',
        'links_limit',
        'sales_usage',
        'sales_limit',
        'domains_limit',
        'tags_limit',
        'folders_usage',
        'folders_limit',
        'users_limit',
        'ai_usage',
        'ai_limit',
        'referral_link_id',
        'referred_signups',
        'store',
        'allowed_hostnames',
        'conversion_enabled',
        'webhook_enabled',
        'partners_enabled',
        'sso_enabled',
        'dot_link_claimed',
        'usage_last_checked',
    ];

    protected $casts = [
        'payment_failed_at' => 'datetime',
        'store' => 'array',
        'allowed_hostnames' => 'array',
        'conversion_enabled' => 'boolean',
        'webhook_enabled' => 'boolean',
        'partners_enabled' => 'boolean',
        'sso_enabled' => 'boolean',
        'dot_link_claimed' => 'boolean',
        'usage_last_checked' => 'datetime',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'workspace_users')
            ->withPivot(['role', 'workspace_preferences', 'default_folder_id'])
            ->withTimestamps();
    }

    public function links(): HasMany
    {
        return $this->hasMany(Link::class);
    }

    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class);
    }
}
