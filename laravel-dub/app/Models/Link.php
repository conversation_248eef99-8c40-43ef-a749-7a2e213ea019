<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Link extends Model
{
    use HasUuids;

    protected $keyType = 'string';
    public $incrementing = false;

    protected $fillable = [
        'domain',
        'key',
        'url',
        'short_link',
        'archived',
        'expires_at',
        'expired_url',
        'password',
        'track_conversion',
        'proxy',
        'title',
        'description',
        'image',
        'video',
        'utm_source',
        'utm_medium',
        'utm_campaign',
        'utm_term',
        'utm_content',
        'rewrite',
        'do_index',
        'ios',
        'android',
        'geo',
        'test_variants',
        'test_started_at',
        'test_completed_at',
        'user_id',
        'workspace_id',
        'program_id',
        'folder_id',
        'external_id',
        'tenant_id',
        'public_stats',
        'clicks',
        'last_clicked',
        'leads',
        'sales',
        'sale_amount',
        'comments',
        'partner_id',
    ];

    protected $casts = [
        'archived' => 'boolean',
        'expires_at' => 'datetime',
        'track_conversion' => 'boolean',
        'proxy' => 'boolean',
        'rewrite' => 'boolean',
        'do_index' => 'boolean',
        'geo' => 'array',
        'test_variants' => 'array',
        'test_started_at' => 'datetime',
        'test_completed_at' => 'datetime',
        'public_stats' => 'boolean',
        'last_clicked' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function workspace(): BelongsTo
    {
        return $this->belongsTo(Workspace::class);
    }
}
