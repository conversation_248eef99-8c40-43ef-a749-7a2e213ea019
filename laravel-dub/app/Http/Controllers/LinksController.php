<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use App\Models\Link;
use App\Models\Domain;
use Illuminate\Support\Str;

class LinksController extends Controller
{
    public function index(Request $request): Response
    {
        $user = $request->user();
        $workspace = $user->workspaces()->first();

        $links = $workspace
            ? $workspace->links()
                ->orderBy('created_at', 'desc')
                ->paginate(20)
            : collect();

        return Inertia::render('Dashboard/Links/Index', [
            'workspace' => $workspace,
            'links' => $links,
        ]);
    }

    public function create(Request $request): Response
    {
        $user = $request->user();
        $workspace = $user->workspaces()->first();

        $domains = $workspace
            ? $workspace->domains()->where('verified', true)->get()
            : collect();

        return Inertia::render('Dashboard/Links/Create', [
            'workspace' => $workspace,
            'domains' => $domains,
        ]);
    }

    public function store(Request $request)
    {
        $user = $request->user();
        $workspace = $user->workspaces()->first();

        if (!$workspace) {
            return back()->withErrors(['error' => 'No workspace found']);
        }

        $validated = $request->validate([
            'url' => 'required|url',
            'key' => 'nullable|string|max:255',
            'domain' => 'required|string',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:280',
        ]);

        // Generate key if not provided
        if (empty($validated['key'])) {
            $validated['key'] = Str::random(6);
        }

        // Check if key already exists for this domain
        $existingLink = Link::where('domain', $validated['domain'])
            ->where('key', $validated['key'])
            ->first();

        if ($existingLink) {
            return back()->withErrors(['key' => 'This short link already exists']);
        }

        $link = Link::create([
            'id' => Str::uuid(),
            'domain' => $validated['domain'],
            'key' => $validated['key'],
            'url' => $validated['url'],
            'short_link' => "https://{$validated['domain']}/{$validated['key']}",
            'title' => $validated['title'],
            'description' => $validated['description'],
            'user_id' => $user->id,
            'workspace_id' => $workspace->id,
        ]);

        // Update workspace stats
        $workspace->increment('total_links');
        $workspace->increment('links_usage');

        return redirect()->route('links.index')->with('success', 'Link created successfully!');
    }

    public function show(Link $link): Response
    {
        return Inertia::render('Dashboard/Links/Show', [
            'link' => $link,
        ]);
    }

    public function edit(Link $link): Response
    {
        $workspace = $link->workspace;
        $domains = $workspace
            ? $workspace->domains()->where('verified', true)->get()
            : collect();

        return Inertia::render('Dashboard/Links/Edit', [
            'link' => $link,
            'workspace' => $workspace,
            'domains' => $domains,
        ]);
    }

    public function update(Request $request, Link $link)
    {
        $validated = $request->validate([
            'url' => 'required|url',
            'key' => 'required|string|max:255',
            'domain' => 'required|string',
            'title' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:280',
        ]);

        // Check if key already exists for this domain (excluding current link)
        $existingLink = Link::where('domain', $validated['domain'])
            ->where('key', $validated['key'])
            ->where('id', '!=', $link->id)
            ->first();

        if ($existingLink) {
            return back()->withErrors(['key' => 'This short link already exists']);
        }

        $link->update([
            'domain' => $validated['domain'],
            'key' => $validated['key'],
            'url' => $validated['url'],
            'short_link' => "https://{$validated['domain']}/{$validated['key']}",
            'title' => $validated['title'],
            'description' => $validated['description'],
        ]);

        return redirect()->route('links.index')->with('success', 'Link updated successfully!');
    }

    public function destroy(Link $link)
    {
        $workspace = $link->workspace;
        $link->delete();

        // Update workspace stats
        if ($workspace) {
            $workspace->decrement('total_links');
            $workspace->decrement('links_usage');
        }

        return redirect()->route('links.index')->with('success', 'Link deleted successfully!');
    }
}
