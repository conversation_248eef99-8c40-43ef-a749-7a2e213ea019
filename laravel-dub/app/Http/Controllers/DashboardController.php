<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function index(Request $request): Response
    {
        $user = $request->user();

        // Get user's default workspace or first workspace
        $workspace = $user->workspaces()->first();

        // Get recent links
        $recentLinks = $workspace
            ? $workspace->links()
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
            : collect();

        // Get analytics data
        $analytics = [
            'total_links' => $workspace?->total_links ?? 0,
            'total_clicks' => $workspace?->total_clicks ?? 0,
            'links_this_month' => $workspace
                ? $workspace->links()
                    ->whereMonth('created_at', now()->month)
                    ->count()
                : 0,
            'clicks_this_month' => $workspace
                ? $workspace->links()
                    ->whereMonth('created_at', now()->month)
                    ->sum('clicks')
                : 0,
        ];

        return Inertia::render('Dashboard/Index', [
            'workspace' => $workspace,
            'recentLinks' => $recentLinks,
            'analytics' => $analytics,
        ]);
    }
}
