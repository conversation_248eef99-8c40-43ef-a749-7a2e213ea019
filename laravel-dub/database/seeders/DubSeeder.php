<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Workspace;
use App\Models\Link;
use App\Models\Domain;
use Illuminate\Support\Str;

class DubSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user
        $user = User::create([
            'id' => Str::uuid(),
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password_hash' => bcrypt('password'),
            'email_verified_at' => now(),
        ]);

        // Create a test workspace
        $workspace = Workspace::create([
            'id' => Str::uuid(),
            'name' => 'My Workspace',
            'slug' => 'my-workspace',
            'plan' => 'free',
            'billing_cycle_start' => now()->day,
            'total_links' => 5,
            'total_clicks' => 150,
        ]);

        // Attach user to workspace
        \DB::table('workspace_users')->insert([
            'id' => Str::uuid(),
            'user_id' => $user->id,
            'workspace_id' => $workspace->id,
            'role' => 'owner',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // Create a test domain
        $domain = Domain::create([
            'id' => Str::uuid(),
            'slug' => 'dub.sh',
            'verified' => true,
            'primary' => true,
            'workspace_id' => $workspace->id,
        ]);

        // Create some test links
        $links = [
            [
                'key' => 'github',
                'url' => 'https://github.com',
                'title' => 'GitHub',
                'description' => 'Where the world builds software',
                'clicks' => 45,
            ],
            [
                'key' => 'google',
                'url' => 'https://google.com',
                'title' => 'Google',
                'description' => 'Search the world\'s information',
                'clicks' => 32,
            ],
            [
                'key' => 'twitter',
                'url' => 'https://twitter.com',
                'title' => 'Twitter',
                'description' => 'What\'s happening?',
                'clicks' => 28,
            ],
            [
                'key' => 'youtube',
                'url' => 'https://youtube.com',
                'title' => 'YouTube',
                'description' => 'Broadcast yourself',
                'clicks' => 25,
            ],
            [
                'key' => 'linkedin',
                'url' => 'https://linkedin.com',
                'title' => 'LinkedIn',
                'description' => 'Professional network',
                'clicks' => 20,
            ],
        ];

        foreach ($links as $linkData) {
            Link::create([
                'id' => Str::uuid(),
                'domain' => $domain->slug,
                'key' => $linkData['key'],
                'url' => $linkData['url'],
                'short_link' => "https://{$domain->slug}/{$linkData['key']}",
                'title' => $linkData['title'],
                'description' => $linkData['description'],
                'clicks' => $linkData['clicks'],
                'user_id' => $user->id,
                'workspace_id' => $workspace->id,
                'last_clicked' => now()->subDays(rand(1, 7)),
            ]);
        }
    }
}
