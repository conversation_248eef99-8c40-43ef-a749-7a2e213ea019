<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workspace_users', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->enum('role', ['owner', 'member'])->default('member');
            $table->string('user_id');
            $table->string('workspace_id');
            $table->json('workspace_preferences')->nullable();
            $table->string('default_folder_id')->nullable();

            $table->timestamps();

            $table->unique(['user_id', 'workspace_id']);
            $table->index('workspace_id');

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('workspace_id')->references('id')->on('workspaces')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workspace_users');
    }
};
