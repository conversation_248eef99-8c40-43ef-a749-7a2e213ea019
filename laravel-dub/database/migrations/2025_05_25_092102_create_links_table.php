<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('links', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('domain');
            $table->string('key');
            $table->longText('url');
            $table->string('short_link', 400)->unique();
            $table->boolean('archived')->default(false);
            $table->timestamp('expires_at')->nullable();
            $table->text('expired_url')->nullable();
            $table->string('password')->nullable();
            $table->boolean('track_conversion')->default(false);

            // OG Tags
            $table->boolean('proxy')->default(false);
            $table->string('title')->nullable();
            $table->string('description', 280)->nullable();
            $table->longText('image')->nullable();
            $table->text('video')->nullable();

            // UTM Parameters
            $table->string('utm_source')->nullable();
            $table->string('utm_medium')->nullable();
            $table->string('utm_campaign')->nullable();
            $table->string('utm_term')->nullable();
            $table->string('utm_content')->nullable();

            // Link settings
            $table->boolean('rewrite')->default(false);
            $table->boolean('do_index')->default(false);

            // Device targeting
            $table->text('ios')->nullable();
            $table->text('android')->nullable();
            $table->json('geo')->nullable();

            // A/B Testing
            $table->json('test_variants')->nullable();
            $table->timestamp('test_started_at')->nullable();
            $table->timestamp('test_completed_at')->nullable();

            // Relations
            $table->string('user_id')->nullable();
            $table->string('workspace_id')->nullable();
            $table->string('program_id')->nullable();
            $table->string('folder_id')->nullable();

            // External IDs
            $table->string('external_id')->nullable();
            $table->string('tenant_id')->nullable();

            // Stats
            $table->boolean('public_stats')->default(false);
            $table->integer('clicks')->default(0);
            $table->timestamp('last_clicked')->nullable();
            $table->integer('leads')->default(0);
            $table->integer('sales')->default(0);
            $table->integer('sale_amount')->default(0);

            // Comments
            $table->text('comments')->nullable();

            // Partner relations
            $table->string('partner_id')->nullable();

            $table->timestamps();

            // Indexes
            $table->unique(['domain', 'key']);
            $table->unique(['workspace_id', 'external_id']);
            $table->index(['workspace_id', 'tenant_id']);
            $table->index(['workspace_id', 'folder_id', 'archived', 'created_at']);
            $table->index(['program_id', 'partner_id']);
            $table->index('folder_id');
            $table->index('user_id');

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->foreign('workspace_id')->references('id')->on('workspaces')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('links');
    }
};
