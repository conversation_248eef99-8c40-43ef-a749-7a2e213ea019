<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domains', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('slug')->unique();
            $table->boolean('verified')->default(false);
            $table->string('placeholder')->nullable();
            $table->longText('expired_url')->nullable();
            $table->longText('not_found_url')->nullable();
            $table->boolean('primary')->default(false);
            $table->boolean('archived')->default(false);
            $table->timestamp('last_checked')->default(now());
            $table->string('logo')->nullable();
            $table->json('apple_app_site_association')->nullable();
            $table->json('asset_links')->nullable();
            $table->string('workspace_id')->nullable();

            $table->timestamps();

            $table->index('workspace_id');
            $table->index('last_checked');

            $table->foreign('workspace_id')->references('id')->on('workspaces')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domains');
    }
};
