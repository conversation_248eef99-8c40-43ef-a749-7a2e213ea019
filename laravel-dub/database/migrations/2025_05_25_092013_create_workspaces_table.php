<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workspaces', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('logo')->nullable();
            $table->string('invite_code')->unique()->nullable();
            $table->string('default_program_id')->unique()->nullable();

            // Billing
            $table->string('plan')->default('free');
            $table->string('stripe_id')->unique()->nullable();
            $table->integer('billing_cycle_start');
            $table->timestamp('payment_failed_at')->nullable();
            $table->string('invoice_prefix')->unique()->nullable();

            // Integrations
            $table->string('stripe_connect_id')->unique()->nullable();
            $table->string('shopify_store_id')->unique()->nullable();

            // Stats
            $table->integer('total_links')->default(0);
            $table->integer('total_clicks')->default(0);

            // Usage limits
            $table->integer('usage')->default(0);
            $table->integer('usage_limit')->default(1000);
            $table->integer('links_usage')->default(0);
            $table->integer('links_limit')->default(25);
            $table->integer('sales_usage')->default(0);
            $table->integer('sales_limit')->default(0);
            $table->integer('domains_limit')->default(3);
            $table->integer('tags_limit')->default(5);
            $table->integer('folders_usage')->default(0);
            $table->integer('folders_limit')->default(0);
            $table->integer('users_limit')->default(1);
            $table->integer('ai_usage')->default(0);
            $table->integer('ai_limit')->default(10);

            // Referrals
            $table->string('referral_link_id')->unique()->nullable();
            $table->integer('referred_signups')->default(0);

            // Settings
            $table->json('store')->nullable();
            $table->json('allowed_hostnames')->nullable();

            // Features
            $table->boolean('conversion_enabled')->default(false);
            $table->boolean('webhook_enabled')->default(false);
            $table->boolean('partners_enabled')->default(false);
            $table->boolean('sso_enabled')->default(false);
            $table->boolean('dot_link_claimed')->default(false);

            $table->timestamps();
            $table->timestamp('usage_last_checked')->default(now());

            $table->index('usage_last_checked');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workspaces');
    }
};
